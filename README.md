# 法盾项目

基于Spring Boot 2.7.15的Java Web应用程序，集成了完整的接口返回体封装体系。

## 项目信息

- **项目名称**: 法盾项目
- **Spring Boot版本**: 2.7.15
- **Java版本**: 8
- **构建工具**: Maven
- **端口**: 8080
- **上下文路径**: /fadun

## 技术栈

- Spring Boot 2.7.15
- Spring Web
- MyBatis Plus
- MySQL
- Lombok
- Knife4j (API文档)
- Maven

## 项目特色

### 🎯 统一返回体封装
- **Result<T>**: 统一API返回格式
- **PageResult<T>**: 分页数据返回格式
- **ResultCode**: 统一状态码枚举
- **ResultUtils**: 返回体工具类
- **BusinessException**: 业务异常类
- **GlobalExceptionHandler**: 全局异常处理

### 📦 战备模块特点
- **纯数量管理**: 专注于物资数量，不涉及金额计算
- **简化设计**: 去除保质期、生产日期等复杂字段
- **简洁表名**: 数据库表名简洁明了，无冗余前缀
- **实用导向**: 适合基础物资管理场景
- **结构清晰**: 删除冗余类，优化包结构



### 📚 完整API文档
- 集成Knife4j，提供美观的API文档界面
- 支持在线测试和调试
- 完整的接口注解和示例

## 快速开始

### 1. 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 运行项目

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

### 3. 访问应用

- 应用地址: http://localhost:8080/fadun
- **API文档**: http://localhost:8080/fadun/doc.html

## API接口

### 系统管理
- **健康检查**: `GET /api/system/health` - 检查应用运行状态
- **欢迎信息**: `GET /api/system/welcome` - 返回欢迎信息
- **系统信息**: `GET /api/system/info` - 获取系统详细信息

### 用户管理
- **用户列表**: `GET /api/user/list` - 分页获取用户列表
- **用户详情**: `GET /api/user/{id}` - 获取用户详细信息
- **创建用户**: `POST /api/user/create` - 创建新用户
- **更新用户**: `PUT /api/user/{id}` - 更新用户信息
- **删除用户**: `DELETE /api/user/{id}` - 删除用户
- **批量删除**: `POST /api/user/batch-delete` - 批量删除用户

### 战备模块
#### 物资台账管理
- **物资列表**: `GET /api/material/page` - 分页查询物资台账
- **物资详情**: `GET /api/material/{id}` - 获取物资详细信息
- **创建物资**: `POST /api/material/create` - 新增物资信息
- **更新物资**: `PUT /api/material/{id}` - 更新物资信息
- **删除物资**: `DELETE /api/material/{id}` - 删除物资信息

#### 物资分类管理
- **分类树**: `GET /api/category/tree` - 获取分类树形结构
- **分类列表**: `GET /api/category/list` - 获取分类列表
- **子分类**: `GET /api/category/children/{parentId}` - 获取子分类
- **分类详情**: `GET /api/category/{id}` - 获取分类详情
- **创建分类**: `POST /api/category/create` - 新增分类
- **更新分类**: `PUT /api/category/{id}` - 修改分类
- **删除分类**: `DELETE /api/category/{id}` - 删除分类
- **编码检查**: `GET /api/category/check-code` - 检查分类编码

#### 库室管理
- **库室列表**: `GET /api/warehouse/page` - 分页查询库室信息
- **库室详情**: `GET /api/warehouse/{id}` - 获取库室详细信息
- **创建库室**: `POST /api/warehouse/create` - 新增库室信息
- **更新库室**: `PUT /api/warehouse/{id}` - 更新库室信息
- **删除库室**: `DELETE /api/warehouse/{id}` - 删除库室信息
- **库室列表**: `GET /api/warehouse/list` - 获取所有启用库室
- **编码检查**: `GET /api/warehouse/check-code` - 检查库室编码
- **库室统计**: `GET /api/warehouse/statistics` - 获取库室统计信息

**库室字段说明**：
- 库室编码（必填）
- 库室名称（必填）
- 位置（必填）
- 管理员ID
- 管理员姓名（必填）
- 联系电话
- 状态（1-正常，0-停用）
- 备注

#### 物资入库管理
- **入库单列表**: `GET /api/inbound/page` - 分页查询入库单
- **入库单详情**: `GET /api/inbound/{id}` - 获取入库单详情
- **创建入库单**: `POST /api/inbound/create` - 新建入库工单
- **确认入库**: `POST /api/inbound/{id}/confirm` - 确认物资入库
- **取消入库**: `POST /api/inbound/{id}/cancel` - 取消入库单

#### 物资出库管理
- **出库单列表**: `GET /api/outbound/page` - 分页查询出库单
- **出库单详情**: `GET /api/outbound/{id}` - 获取出库单详情
- **创建出库单**: `POST /api/outbound/create` - 新建出库工单
- **确认出库**: `POST /api/outbound/{id}/confirm` - 确认物资出库
- **取消出库**: `POST /api/outbound/{id}/cancel` - 取消出库单

#### 库存管理
- **库存查询**: `GET /api/stock/page` - 分页查询库存信息
- **库存统计**: `GET /api/stock/statistics` - 获取库存统计数据
- **低库存预警**: `GET /api/stock/low-stock-alert` - 获取低库存预警
- **创建盘点**: `POST /api/stock/inventory/create` - 创建盘点单
- **完成盘点**: `POST /api/stock/inventory/{id}/complete` - 完成库存盘点

#### 保养记录管理
- **保养记录**: `GET /api/maintenance/page` - 分页查询保养记录
- **创建记录**: `POST /api/maintenance/create` - 新增保养记录
- **保养历史**: `GET /api/maintenance/material/{id}/history` - 查看物资保养历史
- **保养提醒**: `GET /api/maintenance/reminder` - 获取保养提醒列表

#### 模块总览
- **数据总览**: `GET /api/overview/dashboard` - 获取战备模块统计数据
- **功能模块**: `GET /api/overview/modules` - 获取功能模块列表

### API文档
访问 http://localhost:8080/fadun/doc.html 查看完整的API文档，支持在线测试。

## 返回体格式

### 统一返回格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

### 分页返回格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 100,
    "pages": 10,
    "records": [],
    "hasPrevious": false,
    "hasNext": true,
    "isFirst": true,
    "isLast": false
  },
  "timestamp": "2024-01-01T12:00:00",
  "traceId": "abc123def456"
}
```

## 使用示例

### Controller示例
```java
@RestController
@RequestMapping("/api/demo")
public class DemoController {
    
    @GetMapping("/success")
    public Result<String> success() {
        return ResultUtils.success("操作成功", "Hello World");
    }
    
    @GetMapping("/fail")
    public Result<Void> fail() {
        throw BusinessException.of("业务异常");
    }
    
    @GetMapping("/page")
    public Result<PageResult<String>> page() {
        List<String> data = Arrays.asList("item1", "item2");
        return ResultUtils.page(1L, 10L, 2L, data);
    }
}
```

### 异常处理示例
```java
// 抛出业务异常
throw BusinessException.dataNotFound("用户不存在");
throw BusinessException.paramError("参数错误");
throw BusinessException.of(ResultCode.UNAUTHORIZED);
```

## 快速开始

### 环境要求
- JDK 8+
- MySQL 5.7+
- Maven 3.6+

### 运行步骤
1. 克隆项目到本地
2. 修改 `application.yml` 中的数据库配置
3. 创建数据库并执行SQL脚本
   ```sql
   -- 执行主SQL脚本
   source src/main/resources/sql/module.sql

   -- 可选：执行数据验证脚本
   source src/main/resources/sql/data_validation.sql
   ```
4. 运行 `mvn spring-boot:run` 启动项目
5. 访问 http://localhost:8080/fadun/doc.html 查看API文档

### 初始化数据说明
项目包含完整的测试数据：
- **5个库室**: 主库房、备用库房、器材库、危险品库、临时库房
- **10种物资**: 办公用品、电子设备、安全防护用品等
- **完整业务数据**: 入库单、出库单、库存记录、保养记录、盘点记录
- **时间完整性**: 所有记录都包含完整的时间戳信息
- **状态完整性**: 包含各种业务状态的测试数据

## 开发说明

1. 项目使用UTF-8编码
2. 已配置跨域支持
3. 集成了开发工具，支持热重载
4. 使用Lombok简化代码
5. 统一异常处理和返回格式

## 联系我们

- **项目地址**: http://www.fadun.com
- **技术支持**: <EMAIL>
- **版本**: v1.0.0
