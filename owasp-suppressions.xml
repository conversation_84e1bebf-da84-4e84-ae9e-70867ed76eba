<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
    <!-- 
        OWASP Dependency Check 抑制文件
        用于抑制已知的误报或已接受的风险
    -->
    
    <!-- 抑制Spring Boot相关的误报 -->
    <suppress>
        <notes><![CDATA[
            Spring Boot 2.7.15 是一个稳定版本，相关的CVE已在此版本中修复
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/.*@2\.7\.15$</packageUrl>
        <cve>CVE-2023-20860</cve>
        <cve>CVE-2023-20861</cve>
    </suppress>
    
    <!-- 抑制MySQL Connector相关的误报 -->
    <suppress>
        <notes><![CDATA[
            MySQL Connector 8.0.33 已修复了已知的安全漏洞
        ]]></notes>
        <packageUrl regex="true">^pkg:maven/mysql/mysql\-connector\-java@8\.0\.33$</packageUrl>
        <cve>CVE-2023-21971</cve>
    </suppress>
</suppressions>
