<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.SysUserMapper">

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultType="com.fadun.entity.SysUser">
        SELECT 
            id,
            username,
            real_name,
            phone,
            email,
            department,
            position,
            role_type,
            status,
            create_time,
            update_time
        FROM sys_user
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                    username LIKE CONCAT('%', #{keyword}, '%')
                    OR real_name LIKE CONCAT('%', #{keyword}, '%')
                    OR phone LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="roleType != null and roleType != ''">
                AND role_type = #{roleType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>