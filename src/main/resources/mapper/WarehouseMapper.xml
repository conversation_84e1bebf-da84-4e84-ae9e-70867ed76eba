<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.WarehouseMapper">

    <!-- 查询库室及其物资数量 -->
    <select id="selectWarehouseWithMaterialCount" resultType="com.fadun.entity.Warehouse">
        SELECT 
            w.*,
            COALESCE(ms.material_count, 0) as materialCount
        FROM warehouse w
        LEFT JOIN (
            SELECT 
                warehouse_id,
                COUNT(*) as material_count
            FROM material_stock
            GROUP BY warehouse_id
        ) ms ON w.id = ms.warehouse_id
        WHERE w.status = 1
        ORDER BY w.warehouse_name
    </select>

    <!-- 检查库室下是否有物资 -->
    <select id="countMaterialsByWarehouse" resultType="int">
        SELECT COUNT(*)
        FROM material_stock
        WHERE warehouse_id = #{warehouseId}
    </select>

    <!-- 检查库室编码是否存在 -->
    <select id="countByWarehouseCode" resultType="int">
        SELECT COUNT(*)
        FROM warehouse
        WHERE warehouse_code = #{warehouseCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
