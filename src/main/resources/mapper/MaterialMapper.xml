<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.MaterialMapper">

    <!-- 分页查询物资台账 -->
    <select id="selectMaterialPage" resultType="com.fadun.entity.Material">
        SELECT 
            m.id,
            m.material_code,
            m.material_name,
            m.category_id,
            m.specification,
            m.unit,
            m.brand,
            m.model,
            m.status,
            m.remark,
            m.create_time,
            m.update_time,
            m.create_by,
            m.update_by,
            c.category_name
        FROM material m
        LEFT JOIN material_category c ON m.category_id = c.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (m.material_code LIKE CONCAT('%', #{keyword}, '%') 
                     OR m.material_name LIKE CONCAT('%', #{keyword}, '%')
                     OR m.specification LIKE CONCAT('%', #{keyword}, '%')
                     OR m.brand LIKE CONCAT('%', #{keyword}, '%')
                     OR m.model LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="categoryId != null">
                AND m.category_id = #{categoryId}
            </if>
            <if test="warehouseId != null">
                AND EXISTS (
                    SELECT 1 FROM material_stock s 
                    WHERE s.material_id = m.id 
                    AND s.warehouse_id = #{warehouseId}
                )
            </if>
            AND m.status = 1
        </where>
        ORDER BY m.create_time DESC
    </select>

</mapper>
