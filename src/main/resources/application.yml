server:
  port: 8080
  servlet:
    context-path: /fadun

spring:
  application:
    name: fadun-project

  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  # 数据库配置
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: newpower
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
    
  # 开发工具配置
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

# 日志配置
logging:
  level:
    com.fadun: debug
    org.springframework.web: debug
    # 屏蔽 Swagger 参数解析警告
    springfox.documentation.spring.web.readers.operation.OperationImplicitParameterReader: ERROR
    # 屏蔽其他 Swagger 相关警告
    springfox.documentation: ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Knife4j配置
knife4j:
  enable: true
  setting:
    # 是否显示Footer
    enableFooter: false
    # 是否显示主页内容
    enableHomeCustom: true
    # 主页内容Markdown文档
    homeCustomLocation: classpath:markdown/home.md
  # 基础认证
  basic:
    enable: false
    username: admin
    password: 123456
  # 生产环境屏蔽
  production: false
