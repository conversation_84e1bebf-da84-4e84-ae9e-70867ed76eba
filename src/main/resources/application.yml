# 通用配置
server:
  port: 8080
  servlet:
    context-path: /fadun

spring:
  application:
    name: fadun-project

  # 环境配置
  profiles:
    active: dev  # 默认激活开发环境

  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev

  # 数据库配置
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: newpower
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # 开发工具配置
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

# MyBatis Plus配置 - 开发环境
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置 - 开发环境
logging:
  level:
    com.fadun: debug
    org.springframework.web: debug
    # 屏蔽 Swagger 参数解析警告
    springfox.documentation.spring.web.readers.operation.OperationImplicitParameterReader: ERROR
    # 屏蔽其他 Swagger 相关警告
    springfox.documentation: ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Knife4j配置 - 开发环境
knife4j:
  enable: true
  setting:
    enableFooter: false
    enableHomeCustom: true
    homeCustomLocation: classpath:markdown/home.md
  basic:
    enable: false
    username: admin
    password: 123456
  production: false

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

  # 数据库配置
  datasource:
    url: ****************************************************************************************************************
    username: ${DB_USERNAME:fadun_user}
    password: ${DB_PASSWORD:your_prod_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境不自动更新表结构
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

# MyBatis Plus配置 - 生产环境
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true  # 生产环境启用缓存
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl  # 关闭SQL日志
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置 - 生产环境
logging:
  level:
    com.fadun: info  # 生产环境使用info级别
    org.springframework.web: warn
    org.springframework.security: warn
    org.hibernate: warn
    com.zaxxer.hikari: warn
    springfox.documentation: ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/fadun.log
    max-size: 100MB
    max-history: 30

# Knife4j配置 - 生产环境
knife4j:
  enable: false  # 生产环境关闭接口文档
  production: true

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test

  # 数据库配置
  datasource:
    url: ***************************************************************************************************************
    username: ${DB_USERNAME:test_user}
    password: ${DB_PASSWORD:test_password}
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试环境每次重新创建表
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# MyBatis Plus配置 - 测试环境
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置 - 测试环境
logging:
  level:
    com.fadun: debug
    org.springframework.web: info
    springfox.documentation: ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Knife4j配置 - 测试环境
knife4j:
  enable: true
  setting:
    enableFooter: false
    enableHomeCustom: true
    homeCustomLocation: classpath:markdown/home.md
  basic:
    enable: true  # 测试环境启用基础认证
    username: test
    password: test123
  production: false
