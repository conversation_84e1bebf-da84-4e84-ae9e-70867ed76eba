-- 战备模块数据库表结构设计
-- 创建时间：2024-01-01
-- 作者：法盾团队

-- 1. 库室信息表
DROP TABLE IF EXISTS `warehouse`;
CREATE TABLE `warehouse` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `warehouse_code` VARCHAR(50) NOT NULL COMMENT '库室编码',
    `warehouse_name` VARCHAR(100) NOT NULL COMMENT '库室名称',
    `location` VARCHAR(200) COMMENT '库室位置',
    `manager_id` BIGINT COMMENT '管理员ID',
    `manager_name` VARCHAR(50) COMMENT '管理员姓名',
    `manager_phone` VARCHAR(20) COMMENT '联系电话',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用',
    `remark` TEXT COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_warehouse_code` (`warehouse_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库室信息表';

-- 2. 物资分类表
DROP TABLE IF EXISTS `material_category`;
CREATE TABLE `material_category` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `category_code` VARCHAR(50) NOT NULL COMMENT '分类编码',
    `category_name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父级分类ID',
    `level` TINYINT DEFAULT 1 COMMENT '分类层级',
    `remark` TEXT COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_code` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物资分类表';

-- 3. 物资信息表
DROP TABLE IF EXISTS `material`;
CREATE TABLE `material` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `material_code` VARCHAR(50) NOT NULL COMMENT '物资编码',
    `material_name` VARCHAR(200) NOT NULL COMMENT '物资名称',
    `category_id` BIGINT NOT NULL COMMENT '分类ID',
    `specification` VARCHAR(200) COMMENT '规格型号',
    `unit` VARCHAR(20) NOT NULL COMMENT '计量单位',
    `brand` VARCHAR(100) COMMENT '品牌',
    `model` VARCHAR(100) COMMENT '型号',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-停用',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) COMMENT '创建人',
    `update_by` VARCHAR(50) COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_material_code` (`material_code`),
    KEY `idx_category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物资信息表';

-- 4. 物资库存表
DROP TABLE IF EXISTS `material_stock`;
CREATE TABLE `material_stock` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `material_id` BIGINT NOT NULL COMMENT '物资ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '库室ID',
    `current_stock` INT DEFAULT 0 COMMENT '当前库存',
    `location` VARCHAR(100) COMMENT '存放位置',
    `last_in_time` DATETIME COMMENT '最后入库时间',
    `last_out_time` DATETIME COMMENT '最后出库时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_material_warehouse` (`material_id`, `warehouse_id`),
    KEY `idx_warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物资库存表';

-- 5. 入库单表
DROP TABLE IF EXISTS `inbound_order`;
CREATE TABLE `inbound_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '入库单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '库室ID',
    `supplier` VARCHAR(200) COMMENT '供应商',
    `inbound_type` VARCHAR(20) NOT NULL COMMENT '入库类型：purchase-采购，return-退货，transfer-调拨',
    `total_quantity` INT DEFAULT 0 COMMENT '总数量',
    `operator_id` BIGINT COMMENT '操作员ID',
    `operator_name` VARCHAR(50) COMMENT '操作员姓名',
    `inbound_time` DATETIME NOT NULL COMMENT '入库时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待入库，2-已入库，3-已取消',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) COMMENT '创建人',
    `update_by` VARCHAR(50) COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单表';

-- 6. 入库单明细表
DROP TABLE IF EXISTS `inbound_detail`;
CREATE TABLE `inbound_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '入库单ID',
    `material_id` BIGINT NOT NULL COMMENT '物资ID',
    `quantity` INT NOT NULL COMMENT '入库数量',
    `remark` TEXT COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_material_id` (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单明细表';

-- 7. 出库单表
DROP TABLE IF EXISTS `outbound_order`;
CREATE TABLE `outbound_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '出库单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '库室ID',
    `outbound_type` VARCHAR(20) NOT NULL COMMENT '出库类型：use-使用，transfer-调拨，scrap-报废',
    `receiver_unit` VARCHAR(200) COMMENT '接收单位',
    `receiver_name` VARCHAR(50) COMMENT '接收人',
    `receiver_phone` VARCHAR(20) COMMENT '接收人电话',
    `total_quantity` INT DEFAULT 0 COMMENT '总数量',
    `operator_id` BIGINT COMMENT '操作员ID',
    `operator_name` VARCHAR(50) COMMENT '操作员姓名',
    `outbound_time` DATETIME NOT NULL COMMENT '出库时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待出库，2-已出库，3-已取消',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) COMMENT '创建人',
    `update_by` VARCHAR(50) COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库单表';

-- 8. 出库单明细表
DROP TABLE IF EXISTS `outbound_detail`;
CREATE TABLE `outbound_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '出库单ID',
    `material_id` BIGINT NOT NULL COMMENT '物资ID',
    `quantity` INT NOT NULL COMMENT '出库数量',
    `remark` TEXT COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_material_id` (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库单明细表';

-- 9. 盘点单表
DROP TABLE IF EXISTS `inventory_order`;
CREATE TABLE `inventory_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no` VARCHAR(50) NOT NULL COMMENT '盘点单号',
    `warehouse_id` BIGINT NOT NULL COMMENT '库室ID',
    `inventory_type` VARCHAR(20) NOT NULL COMMENT '盘点类型：full-全盘，partial-抽盘',
    `inventory_date` DATE NOT NULL COMMENT '盘点日期',
    `operator_id` BIGINT COMMENT '盘点员ID',
    `operator_name` VARCHAR(50) COMMENT '盘点员姓名',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-盘点中，2-已完成，3-已取消',
    `total_items` INT DEFAULT 0 COMMENT '盘点物资种类数',
    `diff_items` INT DEFAULT 0 COMMENT '差异物资种类数',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) COMMENT '创建人',
    `update_by` VARCHAR(50) COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点单表';

-- 10. 盘点明细表
DROP TABLE IF EXISTS `inventory_detail`;
CREATE TABLE `inventory_detail` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id` BIGINT NOT NULL COMMENT '盘点单ID',
    `material_id` BIGINT NOT NULL COMMENT '物资ID',
    `book_quantity` INT NOT NULL COMMENT '账面数量',
    `actual_quantity` INT COMMENT '实际数量',
    `difference_quantity` INT COMMENT '差异数量',
    `location` VARCHAR(100) COMMENT '存放位置',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-待盘点，2-已盘点',
    `remark` TEXT COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_material_id` (`material_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点明细表';

-- 11. 保养记录表
DROP TABLE IF EXISTS `maintenance_record`;
CREATE TABLE `maintenance_record` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `material_id` BIGINT NOT NULL COMMENT '物资ID',
    `warehouse_id` BIGINT NOT NULL COMMENT '库室ID',
    `maintenance_type` VARCHAR(20) NOT NULL COMMENT '保养类型：routine-例行，special-专项，emergency-应急',
    `maintenance_date` DATE NOT NULL COMMENT '保养日期',
    `maintenance_content` TEXT NOT NULL COMMENT '保养内容',
    `maintenance_result` VARCHAR(20) COMMENT '保养结果：normal-正常，abnormal-异常，repair-需维修',
    `operator_id` BIGINT COMMENT '保养员ID',
    `operator_name` VARCHAR(50) COMMENT '保养员姓名',
    `next_maintenance_date` DATE COMMENT '下次保养日期',
    `remark` TEXT COMMENT '备注',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` VARCHAR(50) COMMENT '创建人',
    `update_by` VARCHAR(50) COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_material_id` (`material_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    KEY `idx_maintenance_date` (`maintenance_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保养记录表';

-- 初始化数据
-- 清空现有数据（按照外键依赖顺序删除）
DELETE FROM `maintenance_record`;
DELETE FROM `inventory_detail`;
DELETE FROM `inventory_order`;
DELETE FROM `outbound_detail`;
DELETE FROM `outbound_order`;
DELETE FROM `inbound_detail`;
DELETE FROM `inbound_order`;
DELETE FROM `material_stock`;
DELETE FROM `material`;
DELETE FROM `material_category`;
DELETE FROM `warehouse`;

-- 插入库室信息
INSERT INTO `warehouse` (`warehouse_code`, `warehouse_name`, `location`, `manager_id`, `manager_name`, `manager_phone`, `status`, `remark`) VALUES
('WH001', '主库房', '1号楼1层', 1001, '张三', '13800001001', 1, '主要存储办公用品和常用物资'),
('WH002', '备用库房', '1号楼2层', 1002, '李四', '13800001002', 1, '备用库房，存储备用物资'),
('WH003', '器材库', '2号楼1层', 1003, '王五', '13800001003', 1, '专门存储电子设备和工具'),
('WH004', '危险品库', '独立建筑', 1004, '赵六', '13800001004', 1, '存储危险品和特殊物资'),
('WH005', '临时库房', '3号楼1层', 1005, '钱七', '13800001005', 1, '临时存储和中转库房');

-- 插入物资分类
INSERT INTO `material_category` (`category_code`, `category_name`, `parent_id`, `level`, `remark`) VALUES
-- 一级分类
('CAT01', '办公用品', 0, 1, '日常办公所需用品'),
('CAT02', '电子设备', 0, 1, '各类电子设备和器材'),
('CAT03', '安全防护', 0, 1, '安全防护用品'),
('CAT04', '维修工具', 0, 1, '维修保养工具'),
('CAT05', '医疗用品', 0, 1, '医疗急救用品'),
-- 二级分类
('CAT0101', '文具用品', 1, 2, '笔、纸张等文具'),
('CAT0102', '办公设备', 1, 2, '打印机、复印机等'),
('CAT0201', '计算机设备', 2, 2, '电脑、服务器等'),
('CAT0202', '通讯设备', 2, 2, '对讲机、电话等'),
('CAT0301', '个人防护', 3, 2, '安全帽、手套等'),
('CAT0401', '电动工具', 4, 2, '电钻、切割机等'),
('CAT0501', '急救用品', 5, 2, '医用耗材等'),
-- 三级分类
('CAT010101', '书写工具', 6, 3, '各类笔类'),
('CAT010102', '纸张用品', 6, 3, '复印纸、便签等'),
('CAT010201', '打印设备', 7, 3, '打印机、复印机'),
('CAT010202', '办公家具', 7, 3, '桌椅、文件柜等'),
('CAT020101', '台式电脑', 8, 3, '台式机及配件'),
('CAT020102', '笔记本电脑', 8, 3, '便携式电脑'),
('CAT020201', '无线通讯', 9, 3, '对讲机、手机等'),
('CAT020202', '有线通讯', 9, 3, '电话、网络设备'),
('CAT030101', '头部防护', 10, 3, '安全帽、防护面罩'),
('CAT030102', '手部防护', 10, 3, '防护手套、护腕'),
('CAT040101', '钻孔工具', 11, 3, '电钻、冲击钻'),
('CAT040102', '切割工具', 11, 3, '切割机、锯子'),
('CAT050101', '包扎用品', 12, 3, '绷带、纱布等'),
('CAT050102', '消毒用品', 12, 3, '酒精、碘伏等');

-- 插入物资信息
INSERT INTO `material` (`material_code`, `material_name`, `category_id`, `specification`, `unit`, `brand`, `model`, `status`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
('MAT000001', 'A4复印纸', 6, '70g 500张/包', '包', '得力', 'DL-A4-70G', 1, '日常办公用纸', '2024-01-01 08:10:00', '2024-01-01 08:10:00', 'admin', 'admin'),
('MAT000002', '签字笔', 6, '0.5mm 黑色', '支', '晨光', 'MG-2000', 1, '办公签字笔', '2024-01-01 08:15:00', '2024-01-01 08:15:00', 'admin', 'admin'),
('MAT000003', '笔记本电脑', 8, 'i5-8G-256G', '台', '联想', 'ThinkPad E14', 1, '办公用笔记本电脑', '2024-01-01 08:20:00', '2024-01-01 08:20:00', 'admin', 'admin'),
('MAT000004', '激光打印机', 7, 'A4黑白', '台', 'HP', 'LaserJet Pro M404n', 1, 'A4黑白激光打印机', '2024-01-01 08:25:00', '2024-01-01 08:25:00', 'admin', 'admin'),
('MAT000005', '安全帽', 10, 'ABS材质', '顶', '3M', 'H-700', 1, '工地安全帽', '2024-01-01 08:30:00', '2024-01-01 08:30:00', 'admin', 'admin'),
('MAT000006', '防护手套', 10, '丁腈材质', '双', '安思尔', 'TouchNTuff 92-600', 1, '一次性防护手套', '2024-01-01 08:35:00', '2024-01-01 08:35:00', 'admin', 'admin'),
('MAT000007', '对讲机', 9, 'UHF频段', '台', '摩托罗拉', 'GP328D', 1, 'UHF数字对讲机', '2024-01-01 08:40:00', '2024-01-01 08:40:00', 'admin', 'admin'),
('MAT000008', '手电筒', 4, 'LED强光', '个', '神火', 'SF-P60', 1, 'LED强光手电筒', '2024-01-01 08:45:00', '2024-01-01 08:45:00', 'admin', 'admin'),
('MAT000009', '医用口罩', 5, '一次性医用', '盒', '3M', '1860', 1, '医用防护口罩', '2024-01-01 08:50:00', '2024-01-01 08:50:00', 'admin', 'admin'),
('MAT000010', '灭火器', 3, '干粉4kg', '个', '海天', 'MFZ/ABC4', 1, '手提式干粉灭火器', '2024-01-01 08:55:00', '2024-01-01 08:55:00', 'admin', 'admin');



-- 插入入库单数据
INSERT INTO `inbound_order` (`order_no`, `warehouse_id`, `supplier`, `inbound_type`, `total_quantity`, `operator_id`, `operator_name`, `inbound_time`, `status`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
('IN202401150001', 1, '得力办公用品有限公司', 'purchase', 100, 2001, '张操作员', '2024-01-15 09:30:00', 2, '办公用品采购入库', '2024-01-15 09:00:00', '2024-01-15 09:30:00', 'admin', 'admin'),
('IN202401100001', 1, '晨光文具股份有限公司', 'purchase', 300, 2002, '李操作员', '2024-01-10 10:15:00', 2, '文具用品批量采购', '2024-01-10 09:45:00', '2024-01-10 10:15:00', 'admin', 'admin'),
('IN202401050001', 2, '联想集团有限公司', 'purchase', 10, 2003, '王操作员', '2024-01-05 11:00:00', 2, '电脑设备采购入库', '2024-01-05 10:30:00', '2024-01-05 11:00:00', 'admin', 'admin'),
('IN202401220001', 3, '3M中国有限公司', 'purchase', 200, 2001, '张操作员', '2024-01-22 08:00:00', 1, '防护用品采购', '2024-01-22 07:30:00', '2024-01-22 08:00:00', 'admin', 'admin'),
('IN202401200001', 1, '海天消防设备公司', 'transfer', 5, 2004, '赵操作员', '2024-01-20 14:00:00', 2, '从其他库房调拨', '2024-01-20 13:30:00', '2024-01-20 14:00:00', 'admin', 'admin');

-- 插入入库明细数据
INSERT INTO `inbound_detail` (`order_id`, `material_id`, `quantity`, `remark`) VALUES
(1, 1, 50, 'A4复印纸入库'),
(1, 2, 50, '签字笔入库'),
(2, 2, 300, '签字笔批量入库'),
(3, 3, 10, '笔记本电脑入库'),
(4, 5, 100, '安全帽入库'),
(4, 6, 100, '防护手套入库'),
(5, 10, 5, '灭火器调拨入库');

-- 插入出库单数据
INSERT INTO `outbound_order` (`order_no`, `warehouse_id`, `outbound_type`, `receiver_unit`, `receiver_name`, `receiver_phone`, `total_quantity`, `operator_id`, `operator_name`, `outbound_time`, `status`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
('OUT202401200001', 1, 'use', '行政部', '刘主任', '13800138001', 20, 2001, '张操作员', '2024-01-20 14:20:00', 2, '行政部办公用品领用', '2024-01-20 14:00:00', '2024-01-20 14:20:00', 'admin', 'admin'),
('OUT202401180001', 1, 'use', '财务部', '陈经理', '13800138002', 30, 2002, '李操作员', '2024-01-18 16:45:00', 2, '财务部文具用品领用', '2024-01-18 16:30:00', '2024-01-18 16:45:00', 'admin', 'admin'),
('OUT202401120001', 2, 'transfer', '分公司', '周主管', '13800138003', 2, 2003, '王操作员', '2024-01-12 09:30:00', 2, '调拨到分公司', '2024-01-12 09:00:00', '2024-01-12 09:30:00', 'admin', 'admin'),
('OUT202401230001', 3, 'use', '工程部', '孙工程师', '13800138004', 15, 2001, '张操作员', '2024-01-23 10:00:00', 1, '工程部防护用品领用', '2024-01-23 09:30:00', '2024-01-23 10:00:00', 'admin', 'admin');

-- 插入出库明细数据
INSERT INTO `outbound_detail` (`order_id`, `material_id`, `quantity`, `remark`) VALUES
(1, 1, 10, 'A4复印纸出库'),
(1, 2, 10, '签字笔出库'),
(2, 2, 30, '签字笔批量出库'),
(3, 3, 2, '笔记本电脑调拨'),
(4, 5, 10, '安全帽领用'),
(4, 6, 5, '防护手套领用');

-- 插入物资库存数据（根据入库出库计算的实际库存）
INSERT INTO `material_stock` (`material_id`, `warehouse_id`, `current_stock`, `location`, `last_in_time`, `last_out_time`, `update_time`) VALUES
(1, 1, 40, 'A区-01架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'),  -- A4复印纸：入库50，出库10，剩余40
(2, 1, 310, 'A区-02架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'), -- 签字笔：入库350，出库40，剩余310
(3, 2, 8, 'B区-01架', '2024-01-05 11:00:00', '2024-01-12 09:30:00', '2024-01-12 09:30:00'),   -- 笔记本电脑：入库10，出库2，剩余8
(4, 1, 3, 'C区-01架', NULL, NULL, '2024-01-01 00:00:00'),                                      -- 激光打印机：初始库存3
(5, 3, 90, 'D区-01架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'),  -- 安全帽：入库100，出库10，剩余90
(6, 3, 95, 'D区-02架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'),  -- 防护手套：入库100，出库5，剩余95
(7, 2, 12, 'B区-02架', NULL, NULL, '2024-01-01 00:00:00'),                                     -- 对讲机：初始库存12
(8, 3, 25, 'D区-03架', NULL, NULL, '2024-01-01 00:00:00'),                                     -- 手电筒：初始库存25
(9, 1, 120, 'A区-03架', NULL, NULL, '2024-01-01 00:00:00'),                                    -- 医用口罩：初始库存120
(10, 4, 15, 'E区-01架', '2024-01-20 14:00:00', NULL, '2024-01-20 14:00:00');                   -- 灭火器：入库5，初始10，总计15

-- 插入保养记录数据
INSERT INTO `maintenance_record` (`material_id`, `warehouse_id`, `maintenance_type`, `maintenance_date`, `maintenance_content`, `maintenance_result`, `operator_id`, `operator_name`, `next_maintenance_date`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
(3, 2, 'routine', '2024-01-15', '清洁屏幕，检查硬件状态，更新系统', 'normal', 3001, '技术员小李', '2024-04-15', '笔记本电脑例行保养', '2024-01-15 14:00:00', '2024-01-15 14:00:00', 'admin', 'admin'),
(4, 1, 'routine', '2024-01-10', '清洁打印机内部，更换硒鼓，校准打印', 'normal', 3002, '技术员小王', '2024-04-10', '激光打印机例行保养', '2024-01-10 15:30:00', '2024-01-10 15:30:00', 'admin', 'admin'),
(7, 2, 'special', '2024-01-18', '检查电池电量，测试通讯功能，更新频道', 'normal', 3001, '技术员小李', '2024-07-18', '对讲机专项检查', '2024-01-18 11:20:00', '2024-01-18 11:20:00', 'admin', 'admin'),
(10, 4, 'routine', '2024-01-20', '检查压力表，测试喷射功能，检查密封性', 'normal', 3003, '技术员小张', '2024-07-20', '灭火器例行检查', '2024-01-20 16:45:00', '2024-01-20 16:45:00', 'admin', 'admin'),
(3, 2, 'emergency', '2024-01-22', '系统故障紧急维修，重装操作系统', 'repair', 3002, '技术员小王', '2024-02-22', '紧急维修，需要进一步观察', '2024-01-22 09:15:00', '2024-01-22 09:15:00', 'admin', 'admin');

-- 插入盘点单数据
INSERT INTO `inventory_order` (`order_no`, `warehouse_id`, `inventory_type`, `operator_id`, `operator_name`, `inventory_date`, `status`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES
('INV202401250001', 1, 'monthly', 4001, '盘点员小赵', '2024-01-25', 2, '1月份例行盘点', '2024-01-25 08:00:00', '2024-01-25 17:00:00', 'admin', 'admin'),
('INV202401260001', 2, 'monthly', 4002, '盘点员小钱', '2024-01-26', 1, '2号库房月度盘点', '2024-01-26 08:00:00', '2024-01-26 08:00:00', 'admin', 'admin'),
('INV202401200001', 3, 'spot', 4001, '盘点员小赵', '2024-01-20', 2, '防护用品抽查盘点', '2024-01-20 10:00:00', '2024-01-20 15:30:00', 'admin', 'admin');

-- 插入盘点明细数据
INSERT INTO `inventory_detail` (`order_id`, `material_id`, `book_quantity`, `actual_quantity`, `difference_quantity`, `location`, `remark`) VALUES
(1, 1, 45, 45, 0, 'A区-01架', '数量一致'),
(1, 2, 280, 278, -2, 'A区-02架', '少2支笔'),
(1, 4, 3, 3, 0, 'C区-01架', '数量一致'),
(2, 3, 8, 8, 0, 'B区-01架', '数量一致'),
(2, 7, 12, 12, 0, 'B区-02架', '数量一致'),
(3, 5, 85, 87, 2, 'D区-01架', '多2顶安全帽'),
(3, 6, 450, 450, 0, 'D区-02架', '数量一致');

-- ========================================
-- 初始化数据统计
-- ========================================
-- 库室数量: 5个
-- 物资分类: 10个 (5个一级分类，5个二级分类)
-- 物资信息: 10种物资
-- 库存记录: 10条库存记录
-- 入库单: 5张入库单，7条明细
-- 出库单: 4张出库单，6条明细
-- 保养记录: 5条保养记录
-- 盘点单: 3张盘点单，7条明细
--
-- 时间范围: 2024年1月1日 - 2024年1月26日
-- 数据状态: 包含已完成和进行中的业务单据
--
-- 注意事项:
-- 1. 所有时间字段都已填充完整的时间戳
-- 2. 包含create_time、update_time等审计字段
-- 3. 业务单据状态完整(待处理、已完成、已取消)
-- 4. 库存数据与出入库记录保持一致性
-- ========================================
