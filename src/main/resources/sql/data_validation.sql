-- 数据验证查询脚本
-- 用于验证初始化数据的完整性和正确性

-- 1. 统计各表数据量
SELECT '库室信息' as table_name, COUNT(*) as record_count FROM warehouse
UNION ALL
SELECT '物资分类', COUNT(*) FROM material_category
UNION ALL
SELECT '物资信息', COUNT(*) FROM material
UNION ALL
SELECT '库存记录', COUNT(*) FROM material_stock
UNION ALL
SELECT '入库单', COUNT(*) FROM inbound_order
UNION ALL
SELECT '入库明细', COUNT(*) FROM inbound_detail
UNION ALL
SELECT '出库单', COUNT(*) FROM outbound_order
UNION ALL
SELECT '出库明细', COUNT(*) FROM outbound_detail
UNION ALL
SELECT '保养记录', COUNT(*) FROM maintenance_record
UNION ALL
SELECT '盘点单', COUNT(*) FROM inventory_order
UNION ALL
SELECT '盘点明细', COUNT(*) FROM inventory_detail;

-- 2. 检查时间字段完整性
SELECT 
    '库室信息时间字段检查' as check_item,
    COUNT(*) as total_records,
    COUNT(create_time) as has_create_time,
    COUNT(update_time) as has_update_time
FROM warehouse
UNION ALL
SELECT 
    '物资信息时间字段检查',
    COUNT(*),
    COUNT(create_time),
    COUNT(update_time)
FROM material
UNION ALL
SELECT 
    '入库单时间字段检查',
    COUNT(*),
    COUNT(create_time),
    COUNT(update_time)
FROM inbound_order;

-- 3. 检查业务单据状态分布
SELECT 
    '入库单状态分布' as status_check,
    status,
    COUNT(*) as count,
    CASE status 
        WHEN 1 THEN '待入库'
        WHEN 2 THEN '已入库'
        WHEN 3 THEN '已取消'
    END as status_name
FROM inbound_order 
GROUP BY status
UNION ALL
SELECT 
    '出库单状态分布',
    status,
    COUNT(*),
    CASE status 
        WHEN 1 THEN '待出库'
        WHEN 2 THEN '已出库'
        WHEN 3 THEN '已取消'
    END
FROM outbound_order 
GROUP BY status;

-- 4. 检查库存数据一致性
SELECT
    m.material_name,
    ms.current_stock,
    w.warehouse_name,
    ms.location
FROM material_stock ms
JOIN material m ON ms.material_id = m.id
JOIN warehouse w ON ms.warehouse_id = w.id
ORDER BY w.warehouse_name, m.material_name;

-- 5. 检查入库出库数量统计
SELECT 
    '入库统计' as type,
    SUM(total_quantity) as total_quantity,
    COUNT(*) as order_count
FROM inbound_order WHERE status = 2
UNION ALL
SELECT 
    '出库统计',
    SUM(total_quantity),
    COUNT(*)
FROM outbound_order WHERE status = 2;

-- 6. 检查保养记录时间分布
SELECT 
    maintenance_type,
    COUNT(*) as count,
    MIN(maintenance_date) as earliest_date,
    MAX(maintenance_date) as latest_date
FROM maintenance_record
GROUP BY maintenance_type;

-- 7. 检查物资分类层级结构
SELECT
    CASE WHEN level = 1 THEN '一级分类' ELSE '二级分类' END as category_level,
    category_name,
    parent_id,
    sort_order,
    remark
FROM material_category
ORDER BY level, sort_order;

-- 8. 检查库存数据
SELECT
    m.material_name,
    ms.current_stock,
    w.warehouse_name
FROM material m
JOIN material_stock ms ON m.id = ms.material_id
JOIN warehouse w ON ms.warehouse_id = w.id
ORDER BY ms.current_stock DESC;
