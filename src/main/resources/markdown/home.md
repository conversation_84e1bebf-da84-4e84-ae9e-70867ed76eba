# 法盾项目API文档

## 项目简介

欢迎使用法盾项目API文档！本项目基于Spring Boot 2.7.15构建，提供完整的RESTful API接口。

## 技术栈

- **框架**: Spring Boot 2.7.15
- **文档**: Knife4j 3.0.3
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus 3.5.4.1
- **JSON**: FastJSON2 2.0.43

## 接口说明

### 认证方式

本API使用Token认证方式，请在请求头中添加：
```
Authorization: Bearer {your_token}
```

### 响应格式

所有接口统一返回格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 快速开始

1. 选择左侧接口分组
2. 点击具体接口查看详情
3. 填写必要参数
4. 点击"试一下"测试接口

## 联系我们

- **项目地址**: http://www.fadun.com
- **技术支持**: <EMAIL>
- **版本**: v1.0.0

---

*本文档由Knife4j自动生成，最后更新时间：2024-01-01*
