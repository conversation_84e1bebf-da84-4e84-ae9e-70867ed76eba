package com.fadun.controller;

import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.MaterialCategory;
import com.fadun.service.MaterialCategoryService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 物资分类管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-物资分类", description = "物资分类管理")
@RestController
@RequestMapping("/api/category")
@RequiredArgsConstructor
public class MaterialCategoryController {

    private final MaterialCategoryService categoryService;

    /**
     * 获取分类树形结构
     */
    @ApiOperation(value = "获取分类树形结构", notes = "获取完整的分类树形结构，用于前端树形组件")
    @GetMapping("/tree")
    public Result<List<MaterialCategory>> getCategoryTree() {
        List<MaterialCategory> tree = categoryService.getCategoryTree();
        return ResultUtils.success("获取分类树成功", tree);
    }

    /**
     * 获取分类列表
     */
    @ApiOperation(value = "获取分类列表", notes = "获取分类列表，按层级和排序排列")
    @GetMapping("/list")
    public Result<List<MaterialCategory>> getCategoryList() {
        List<MaterialCategory> list = categoryService.getCategoryList();
        return ResultUtils.success("获取分类列表成功", list);
    }

    /**
     * 获取子分类列表
     */
    @ApiOperation(value = "获取子分类列表", notes = "根据父分类ID获取子分类列表")
    @ApiImplicitParam(name = "parentId", value = "父分类ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/children/{parentId}")
    public Result<List<MaterialCategory>> getChildrenCategories(@PathVariable Long parentId) {
        List<MaterialCategory> children = categoryService.getChildrenCategories(parentId);
        return ResultUtils.success("获取子分类成功", children);
    }

    /**
     * 获取分类详情
     */
    @ApiOperation(value = "获取分类详情", notes = "根据分类ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "分类ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/{id}")
    public Result<MaterialCategory> getCategoryDetail(@PathVariable Long id) {
        MaterialCategory category = categoryService.getById(id);
        return ResultUtils.data(category, "获取分类详情成功", "分类不存在");
    }

    /**
     * 创建分类
     */
    @ApiOperation(value = "创建分类", notes = "新增物资分类")
    @PostMapping("/create")
    public Result<Void> createCategory(@RequestBody @Valid @ApiParam("分类信息") MaterialCategory category) {
        boolean success = categoryService.createCategory(category);
        return ResultUtils.condition(success, "创建分类成功", "创建分类失败");
    }

    /**
     * 更新分类
     */
    @ApiOperation(value = "更新分类", notes = "修改分类信息")
    @ApiImplicitParam(name = "id", value = "分类ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PutMapping("/{id}")
    public Result<Void> updateCategory(
            @PathVariable Long id,
            @RequestBody @Valid @ApiParam("分类信息") MaterialCategory category) {
        
        category.setId(id);
        boolean success = categoryService.updateCategory(category);
        return ResultUtils.condition(success, "更新分类成功", "更新分类失败");
    }

    /**
     * 删除分类
     */
    @ApiOperation(value = "删除分类", notes = "删除分类（需检查是否有子分类或物资）")
    @ApiImplicitParam(name = "id", value = "分类ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @DeleteMapping("/{id}")
    public Result<Void> deleteCategory(@PathVariable Long id) {
        boolean success = categoryService.deleteCategory(id);
        return ResultUtils.condition(success, "删除分类成功", "删除分类失败");
    }

    /**
     * 检查分类编码是否存在
     */
    @ApiOperation(value = "检查分类编码", notes = "检查分类编码是否已存在")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryCode", value = "分类编码", required = true, dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "excludeId", value = "排除的分类ID", dataTypeClass = Long.class, paramType = "query")
    })
    @GetMapping("/check-code")
    public Result<Boolean> checkCategoryCode(
            @RequestParam String categoryCode,
            @RequestParam(required = false) Long excludeId) {
        
        boolean exists = categoryService.existsCategoryCode(categoryCode, excludeId);
        return ResultUtils.success("检查完成", !exists);
    }
}
