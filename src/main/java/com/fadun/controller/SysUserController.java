package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.SysUser;
import com.fadun.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 系统用户管理控制器
 * 注意：此控制器只有超级管理员可以访问
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/system/user")
@Api(tags = "系统用户管理")
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 权限检查：只有超级管理员可以访问用户管理功能
     */
    private boolean checkSuperAdminPermission(HttpServletRequest request) {
        // TODO: 从session或token中获取当前登录用户信息
        // 这里暂时返回true，实际项目中需要实现具体的权限验证逻辑
        // SysUser currentUser = getCurrentUser(request);
        // return currentUser != null && currentUser.isSuperAdmin();
        return true;
    }

    @GetMapping("/page")
    @ApiOperation("分页查询用户列表")
    public Result<PageResult<SysUser>> getUserPage(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("角色类型") @RequestParam(required = false) String roleType,
            @ApiParam("状态") @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<PageResult<SysUser>>fail(403, "只有超级管理员可以访问用户管理");
        }

        PageResult<SysUser> result = sysUserService.getUserPage(current, size, keyword, roleType, status);
        return ResultUtils.success(result);
    }

    @PostMapping
    @ApiOperation("创建用户")
    public Result<Boolean> createUser(@RequestBody SysUser user, HttpServletRequest request) {
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员可以创建用户");
        }

        boolean success = sysUserService.createUser(user);
        if (success) {
            log.info("创建用户成功：{}", user.getUsername());
            return ResultUtils.success("用户创建成功", true);
        } else {
            return ResultUtils.fail("用户创建失败");
        }
    }

    @PutMapping("/{id}")
    @ApiOperation("更新用户信息")
    public Result<Boolean> updateUser(
            @ApiParam("用户ID") @PathVariable Long id,
            @RequestBody SysUser user, 
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员可以修改用户信息");
        }

        user.setId(id);
        boolean success = sysUserService.updateUser(user);
        if (success) {
            log.info("更新用户成功：{}", user.getUsername());
            return ResultUtils.success("用户信息更新成功", true);
        } else {
            return ResultUtils.fail("用户信息更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除用户")
    public Result<Boolean> deleteUser(@ApiParam("用户ID") @PathVariable Long id, HttpServletRequest request) {
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员可以删除用户");
        }

        boolean success = sysUserService.deleteUser(id);
        if (success) {
            log.info("删除用户成功，ID：{}", id);
            return ResultUtils.success("用户删除成功", true);
        } else {
            return ResultUtils.fail("用户删除失败");
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("获取用户详情")
    public Result<SysUser> getUserDetail(@ApiParam("用户ID") @PathVariable Long id, HttpServletRequest request) {
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<SysUser>fail(403, "只有超级管理员可以查看用户详情");
        }

        SysUser user = sysUserService.getUserDetail(id);
        return ResultUtils.success(user);
    }

    @PutMapping("/{id}/password")
    @ApiOperation("重置用户密码")
    public Result<Boolean> resetPassword(
            @ApiParam("用户ID") @PathVariable Long id,
            @ApiParam("新密码") @RequestParam String newPassword,
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员可以重置密码");
        }

        boolean success = sysUserService.resetPassword(id, newPassword);
        if (success) {
            log.info("重置用户密码成功，用户ID：{}", id);
            return ResultUtils.success("密码重置成功", true);
        } else {
            return ResultUtils.fail("密码重置失败");
        }
    }

    @PutMapping("/{id}/status")
    @ApiOperation("启用/禁用用户")
    public Result<Boolean> updateUserStatus(
            @ApiParam("用户ID") @PathVariable Long id,
            @ApiParam("状态：1-启用，0-禁用") @RequestParam Integer status,
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员可以修改用户状态");
        }

        boolean success = sysUserService.updateUserStatus(id, status);
        if (success) {
            String operation = status == 1 ? "启用" : "禁用";
            log.info("{}用户成功，用户ID：{}", operation, id);
            return ResultUtils.success(operation + "用户成功", true);
        } else {
            return ResultUtils.fail("操作失败");
        }
    }

    @GetMapping("/roles")
    @ApiOperation("获取所有角色类型")
    public Result<Object> getRoleTypes(HttpServletRequest request) {
        // 权限检查
        if (!checkSuperAdminPermission(request)) {
            return ResultUtils.<Object>fail(403, "只有超级管理员可以查看角色信息");
        }

        Object[] roles = new Object[]{
            new Object[]{SysUser.RoleType.SUPER_ADMIN, "超级管理员"},
            new Object[]{SysUser.RoleType.LOGISTICS_ADMIN, "战备管理员"},
            new Object[]{SysUser.RoleType.TRAINING_ADMIN, "训练管理员"},
            new Object[]{SysUser.RoleType.WORK_ADMIN, "工作管理员"},
            new Object[]{SysUser.RoleType.LIFE_ADMIN, "生活管理员"},
            new Object[]{SysUser.RoleType.NORMAL_USER, "普通用户"}
        };
        
        return ResultUtils.success(roles);
    }

    // TODO: 实现用户登录接口
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<SysUser> login(@RequestParam String username, @RequestParam String password) {
        SysUser user = sysUserService.login(username, password);
        if (user != null) {
            // TODO: 生成并返回token
            log.info("用户登录成功：{}", username);
            return ResultUtils.success("登录成功", user);
        } else {
            return ResultUtils.fail("用户名或密码错误");
        }
    }
}
