package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.Material;
import com.fadun.service.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 物资管理控制器
 * 权限：超级管理员 + 战备管理员
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/material")
@Api(tags = "物资管理")
public class MaterialController {

    @Autowired
    private MaterialService materialService;

    /**
     * 权限检查：超级管理员和战备管理员可以访问
     */
    private boolean checkLogisticsPermission(HttpServletRequest request) {
        // TODO: 从session或token中获取当前登录用户信息
        // 验证用户是否为SUPER_ADMIN或LOGISTICS_ADMIN
        // SysUser currentUser = getCurrentUser(request);
        // return currentUser != null && currentUser.hasModuleAccess("logistics");
        return true;
    }

    @GetMapping("/page")
    @ApiOperation("分页查询物资台账")
    public Result<PageResult<Material>> getMaterialPage(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("分类ID") @RequestParam(required = false) Long categoryId,
            @ApiParam("库室ID") @RequestParam(required = false) Long warehouseId,
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<PageResult<Material>>fail(403, "只有超级管理员和战备管理员可以访问物资管理");
        }

        PageResult<Material> result = materialService.getMaterialPage(current, size, keyword, categoryId, warehouseId);
        return ResultUtils.success(result);
    }

    @PostMapping
    @ApiOperation("创建物资信息")
    public Result<Boolean> createMaterial(@RequestBody Material material, HttpServletRequest request) {
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员和战备管理员可以创建物资");
        }

        boolean success = materialService.createMaterial(material);
        if (success) {
            log.info("创建物资成功：{}", material.getMaterialName());
            return ResultUtils.success("物资创建成功", true);
        } else {
            return ResultUtils.fail("物资创建失败");
        }
    }

    @PutMapping("/{id}")
    @ApiOperation("更新物资信息")
    public Result<Boolean> updateMaterial(
            @ApiParam("物资ID") @PathVariable Long id,
            @RequestBody Material material, 
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员和战备管理员可以修改物资信息");
        }

        material.setId(id);
        boolean success = materialService.updateMaterial(material);
        if (success) {
            log.info("更新物资成功：{}", material.getMaterialName());
            return ResultUtils.success("物资信息更新成功", true);
        } else {
            return ResultUtils.fail("物资信息更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除物资信息")
    public Result<Boolean> deleteMaterial(@ApiParam("物资ID") @PathVariable Long id, HttpServletRequest request) {
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员和战备管理员可以删除物资");
        }

        boolean success = materialService.deleteMaterial(id);
        if (success) {
            log.info("删除物资成功，ID：{}", id);
            return ResultUtils.success("物资删除成功", true);
        } else {
            return ResultUtils.fail("物资删除失败");
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("获取物资详情")
    public Result<Material> getMaterialDetail(@ApiParam("物资ID") @PathVariable Long id, HttpServletRequest request) {
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Material>fail(403, "只有超级管理员和战备管理员可以查看物资详情");
        }

        Material material = materialService.getMaterialDetail(id);
        return ResultUtils.success(material);
    }
}
