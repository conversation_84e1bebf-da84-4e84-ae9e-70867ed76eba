package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.Material;
import com.fadun.service.MaterialService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 物资台账管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-物资台账", description = "物资台账信息管理")
@RestController
@RequestMapping("/api/material")
@RequiredArgsConstructor
public class MaterialController {

    private final MaterialService materialService;

    /**
     * 分页查询物资台账
     */
    @ApiOperation(value = "分页查询物资台账", notes = "获取物资台账信息列表，支持关键词搜索和分类筛选")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页码", defaultValue = "1", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "10", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（物资名称/编码）", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "categoryId", value = "物资分类ID", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "warehouseId", value = "库室ID", dataTypeClass = Long.class, paramType = "query")
    })
    @GetMapping("/page")
    public Result<PageResult<Material>> getMaterialPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long warehouseId) {
        
        PageResult<Material> pageResult = materialService.getMaterialPage(current, size, keyword, categoryId, warehouseId);
        return ResultUtils.page("查询物资台账成功", pageResult);
    }

    /**
     * 获取物资详情
     */
    @ApiOperation(value = "获取物资详情", notes = "根据物资ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "物资ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/{id}")
    public Result<Material> getMaterialDetail(@PathVariable Long id) {
        Material material = materialService.getMaterialDetail(id);
        return ResultUtils.success("获取物资详情成功", material);
    }

    /**
     * 创建物资信息
     */
    @ApiOperation(value = "创建物资信息", notes = "新增物资台账信息")
    @PostMapping("/create")
    public Result<Void> createMaterial(@RequestBody @Valid @ApiParam("物资信息") Material material) {
        boolean success = materialService.createMaterial(material);
        return ResultUtils.condition(success, "创建物资成功", "创建物资失败");
    }

    /**
     * 更新物资信息
     */
    @ApiOperation(value = "更新物资信息", notes = "修改物资台账信息")
    @ApiImplicitParam(name = "id", value = "物资ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PutMapping("/{id}")
    public Result<Void> updateMaterial(
            @PathVariable Long id,
            @RequestBody @Valid @ApiParam("物资信息") Material material) {
        
        material.setId(id);
        boolean success = materialService.updateMaterial(material);
        return ResultUtils.condition(success, "更新物资成功", "更新物资失败");
    }

    /**
     * 删除物资信息
     */
    @ApiOperation(value = "删除物资信息", notes = "删除物资台账信息")
    @ApiImplicitParam(name = "id", value = "物资ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @DeleteMapping("/{id}")
    public Result<Void> deleteMaterial(@PathVariable Long id) {
        boolean success = materialService.deleteMaterial(id);
        return ResultUtils.condition(success, "删除物资成功", "删除物资失败");
    }
}
