package com.fadun.controller;

import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.OutboundDetail;
import com.fadun.service.OutboundDetailService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出库单明细管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-出库明细", description = "出库单明细管理")
@RestController
@RequestMapping("/api/outbound-detail")
@RequiredArgsConstructor
public class OutboundDetailController {

    private final OutboundDetailService outboundDetailService;

    /**
     * 根据出库单ID查询明细列表
     */
    @ApiOperation(value = "查询出库明细", notes = "根据出库单ID查询明细列表")
    @ApiImplicitParam(name = "orderId", value = "出库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/order/{orderId}")
    public Result<List<OutboundDetail>> getDetailsByOrderId(@PathVariable Long orderId) {
        List<OutboundDetail> details = outboundDetailService.getDetailsByOrderId(orderId);
        return ResultUtils.success("查询出库明细成功", details);
    }

    /**
     * 批量保存出库明细
     */
    @ApiOperation(value = "批量保存明细", notes = "批量保存出库明细")
    @PostMapping("/batch")
    public Result<Void> saveDetails(@RequestBody @ApiParam("明细列表") List<OutboundDetail> details) {
        boolean success = outboundDetailService.saveDetails(details);
        return ResultUtils.condition(success, "保存明细成功", "保存明细失败");
    }

    /**
     * 删除出库单的所有明细
     */
    @ApiOperation(value = "删除明细", notes = "删除出库单的所有明细")
    @ApiImplicitParam(name = "orderId", value = "出库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @DeleteMapping("/order/{orderId}")
    public Result<Void> deleteByOrderId(@PathVariable Long orderId) {
        boolean success = outboundDetailService.deleteByOrderId(orderId);
        return ResultUtils.condition(success, "删除明细成功", "删除明细失败");
    }
}
