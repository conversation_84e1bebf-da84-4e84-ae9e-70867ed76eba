package com.fadun.controller;

import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.MaterialStock;
import com.fadun.service.MaterialStockService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物资库存管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-库存管理", description = "物资库存管理")
@RestController
@RequestMapping("/api/material-stock")
@RequiredArgsConstructor
public class MaterialStockController {

    private final MaterialStockService materialStockService;

    /**
     * 获取物资总库存信息
     */
    @ApiOperation(value = "获取物资总库存", notes = "获取所有物资的总库存信息（包含所有库房的库存汇总）")
    @GetMapping("/total")
    public Result<List<MaterialStock>> getMaterialWithTotalStock() {
        List<MaterialStock> stockList = materialStockService.getMaterialWithTotalStock();
        return ResultUtils.success("查询物资总库存成功", stockList);
    }

    /**
     * 检查库存是否充足
     */
    @ApiOperation(value = "检查库存", notes = "检查指定物资在指定库房的库存是否充足")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "materialId", value = "物资ID", required = true, dataTypeClass = Long.class, paramType = "query"),
        @ApiImplicitParam(name = "warehouseId", value = "库房ID", required = true, dataTypeClass = Long.class, paramType = "query"),
        @ApiImplicitParam(name = "quantity", value = "需要数量", required = true, dataTypeClass = Integer.class, paramType = "query")
    })
    @GetMapping("/check")
    public Result<Boolean> checkStockSufficient(
            @RequestParam Long materialId,
            @RequestParam Long warehouseId,
            @RequestParam Integer quantity) {
        boolean sufficient = materialStockService.checkStockSufficient(materialId, warehouseId, quantity);
        return ResultUtils.success("检查库存完成", sufficient);
    }
}
