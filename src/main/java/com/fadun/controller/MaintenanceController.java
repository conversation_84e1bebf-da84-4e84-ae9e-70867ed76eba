package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.MaintenanceRecord;
import com.fadun.service.MaintenanceService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 保养记录管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-保养记录", description = "物资保养记录管理")
@RestController
@RequestMapping("/api/maintenance")
@RequiredArgsConstructor
public class MaintenanceController {

    private final MaintenanceService maintenanceService;

    /**
     * 分页查询保养记录
     */
    @ApiOperation(value = "分页查询保养记录", notes = "获取物资保养记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页码", defaultValue = "1", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "10", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "materialId", value = "物资ID", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "maintenanceType", value = "保养类型", dataTypeClass = String.class, paramType = "query")
    })
    @GetMapping("/page")
    public Result<PageResult<MaintenanceRecord>> getMaintenancePage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long materialId,
            @RequestParam(required = false) String maintenanceType) {
        
        PageResult<MaintenanceRecord> pageResult = maintenanceService.getMaintenancePage(
                current, size, keyword, materialId, maintenanceType);
        return ResultUtils.page("查询保养记录成功", pageResult);
    }

    /**
     * 获取保养记录详情
     */
    @ApiOperation(value = "获取保养记录详情", notes = "根据记录ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "记录ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/{id}")
    public Result<MaintenanceRecord> getMaintenanceDetail(@PathVariable Long id) {
        MaintenanceRecord record = maintenanceService.getById(id);
        return ResultUtils.data(record, "获取保养记录详情成功", "保养记录不存在");
    }

    /**
     * 创建保养记录
     */
    @ApiOperation(value = "创建保养记录", notes = "新增物资保养记录")
    @PostMapping("/create")
    public Result<Void> createMaintenanceRecord(@RequestBody @Valid @ApiParam("保养记录") MaintenanceRecord record) {
        boolean success = maintenanceService.createMaintenanceRecord(record);
        return ResultUtils.condition(success, "创建保养记录成功", "创建保养记录失败");
    }

    /**
     * 更新保养记录
     */
    @ApiOperation(value = "更新保养记录", notes = "修改保养记录信息")
    @ApiImplicitParam(name = "id", value = "记录ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PutMapping("/{id}")
    public Result<Void> updateMaintenanceRecord(
            @PathVariable Long id,
            @RequestBody @Valid @ApiParam("保养记录") MaintenanceRecord record) {
        
        record.setId(id);
        boolean success = maintenanceService.updateById(record);
        return ResultUtils.condition(success, "更新保养记录成功", "更新保养记录失败");
    }

    /**
     * 删除保养记录
     */
    @ApiOperation(value = "删除保养记录", notes = "删除保养记录")
    @ApiImplicitParam(name = "id", value = "记录ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @DeleteMapping("/{id}")
    public Result<Void> deleteMaintenanceRecord(@PathVariable Long id) {
        boolean success = maintenanceService.removeById(id);
        return ResultUtils.condition(success, "删除保养记录成功", "删除保养记录失败");
    }

    /**
     * 获取物资历史保养记录
     */
    @ApiOperation(value = "获取物资历史保养记录", notes = "查看指定物资的所有保养记录")
    @ApiImplicitParam(name = "materialId", value = "物资ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/material/{materialId}/history")
    public Result<List<MaintenanceRecord>> getMaterialMaintenanceHistory(@PathVariable Long materialId) {
        List<MaintenanceRecord> records = maintenanceService.getMaterialMaintenanceHistory(materialId);
        return ResultUtils.success("获取保养历史成功", records);
    }

    /**
     * 获取保养提醒列表
     */
    @ApiOperation(value = "获取保养提醒列表", notes = "获取需要保养的物资列表")
    @GetMapping("/reminder")
    public Result<List<MaintenanceRecord>> getMaintenanceReminder() {
        List<MaintenanceRecord> reminders = maintenanceService.getMaintenanceReminder();
        return ResultUtils.success("获取保养提醒成功", reminders);
    }
}
