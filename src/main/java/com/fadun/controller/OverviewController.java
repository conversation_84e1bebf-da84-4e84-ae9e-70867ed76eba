package com.fadun.controller;

import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 战备模块总览控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-总览", description = "战备模块数据总览和统计")
@RestController
@RequestMapping("/api/overview")
@RequiredArgsConstructor
public class OverviewController {

    /**
     * 获取战备模块总览数据
     */
    @ApiOperation(value = "获取战备模块总览", notes = "获取战备模块各项数据统计")
    @GetMapping("/dashboard")
    public Result<Map<String, Object>> getDashboard() {
        Map<String, Object> dashboard = new HashMap<>();
        
        // 物资统计
        Map<String, Object> materialStats = new HashMap<>();
        materialStats.put("totalMaterials", 156);
        materialStats.put("totalCategories", 12);
        materialStats.put("lowStockCount", 8);
        dashboard.put("materialStats", materialStats);
        
        // 库存统计
        Map<String, Object> stockStats = new HashMap<>();
        stockStats.put("totalStock", 2580);
        stockStats.put("warehouseCount", 5);
        stockStats.put("utilizationRate", 78.5);
        dashboard.put("stockStats", stockStats);

        // 入库统计（本月）
        Map<String, Object> inboundStats = new HashMap<>();
        inboundStats.put("monthlyInbound", 45);
        inboundStats.put("monthlyInboundQuantity", 1250);
        inboundStats.put("pendingInbound", 3);
        dashboard.put("inboundStats", inboundStats);

        // 出库统计（本月）
        Map<String, Object> outboundStats = new HashMap<>();
        outboundStats.put("monthlyOutbound", 38);
        outboundStats.put("monthlyOutboundQuantity", 980);
        outboundStats.put("pendingOutbound", 2);
        dashboard.put("outboundStats", outboundStats);
        
        // 保养统计
        Map<String, Object> maintenanceStats = new HashMap<>();
        maintenanceStats.put("monthlyMaintenance", 25);
        maintenanceStats.put("pendingMaintenance", 12);
        maintenanceStats.put("overdueMaintenance", 2);
        dashboard.put("maintenanceStats", maintenanceStats);
        
        // 最近活动
        dashboard.put("lastUpdateTime", LocalDateTime.now());
        
        return ResultUtils.success("获取战备模块总览成功", dashboard);
    }

    /**
     * 获取模块功能列表
     */
    @ApiOperation(value = "获取模块功能列表", notes = "获取战备模块所有功能模块")
    @GetMapping("/modules")
    public Result<Map<String, Object>> getModules() {
        Map<String, Object> modules = new HashMap<>();
        
        // 物资台账
        Map<String, Object> materialModule = new HashMap<>();
        materialModule.put("name", "物资台账");
        materialModule.put("description", "管辖范围内的某类资源台账信息管理及资源实力统计");
        materialModule.put("icon", "material");
        materialModule.put("path", "/readiness/material");
        materialModule.put("enabled", true);

        // 物资分类
        Map<String, Object> categoryModule = new HashMap<>();
        categoryModule.put("name", "物资分类");
        categoryModule.put("description", "物资分类管理，支持树形结构和排序");
        categoryModule.put("icon", "category");
        categoryModule.put("path", "/readiness/category");
        categoryModule.put("enabled", true);

        // 物资入库
        Map<String, Object> inboundModule = new HashMap<>();
        inboundModule.put("name", "物资入库");
        inboundModule.put("description", "完成物资入库操作，选择库室信息后进行物资上架");
        inboundModule.put("icon", "inbound");
        inboundModule.put("path", "/readiness/inbound");
        inboundModule.put("enabled", true);
        
        // 物资出库
        Map<String, Object> outboundModule = new HashMap<>();
        outboundModule.put("name", "物资出库");
        outboundModule.put("description", "依据出库指令对物资进行出库，创建出库工单");
        outboundModule.put("icon", "outbound");
        outboundModule.put("path", "/readiness/outbound");
        outboundModule.put("enabled", true);
        
        // 库存盘点
        Map<String, Object> inventoryModule = new HashMap<>();
        inventoryModule.put("name", "库存盘点");
        inventoryModule.put("description", "对物资进行盘点核查，保证库存数量正确");
        inventoryModule.put("icon", "inventory");
        inventoryModule.put("path", "/readiness/inventory");
        inventoryModule.put("enabled", true);
        
        // 保养记录
        Map<String, Object> maintenanceModule = new HashMap<>();
        maintenanceModule.put("name", "保养记录");
        maintenanceModule.put("description", "对物资的保养情况进行记录，查看历史保养记录");
        maintenanceModule.put("icon", "maintenance");
        maintenanceModule.put("path", "/readiness/maintenance");
        maintenanceModule.put("enabled", true);
        
        modules.put("material", materialModule);
        modules.put("category", categoryModule);
        modules.put("inbound", inboundModule);
        modules.put("outbound", outboundModule);
        modules.put("inventory", inventoryModule);
        modules.put("maintenance", maintenanceModule);
        
        return ResultUtils.success("获取模块功能列表成功", modules);
    }
}
