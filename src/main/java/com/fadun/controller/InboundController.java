package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.InboundOrder;
import com.fadun.service.InboundService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 物资入库管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-物资入库", description = "物资入库操作管理")
@RestController
@RequestMapping("/api/inbound")
@RequiredArgsConstructor
public class InboundController {

    private final InboundService inboundService;

    /**
     * 分页查询入库单
     */
    @ApiOperation(value = "分页查询入库单", notes = "获取入库单列表，支持按类型和状态筛选")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页码", defaultValue = "1", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "10", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（入库单号/供应商）", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "inboundType", value = "入库类型", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态", dataTypeClass = Integer.class, paramType = "query")
    })
    @GetMapping("/page")
    public Result<PageResult<InboundOrder>> getInboundPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String inboundType,
            @RequestParam(required = false) Integer status) {
        
        PageResult<InboundOrder> pageResult = inboundService.getInboundPage(current, size, keyword, inboundType, status);
        return ResultUtils.page("查询入库单成功", pageResult);
    }

    /**
     * 获取入库单详情
     */
    @ApiOperation(value = "获取入库单详情", notes = "根据入库单ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "入库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/{id}")
    public Result<InboundOrder> getInboundDetail(@PathVariable Long id) {
        InboundOrder inboundOrder = inboundService.getInboundDetail(id);
        return ResultUtils.success("获取入库单详情成功", inboundOrder);
    }

    /**
     * 创建入库单
     */
    @ApiOperation(value = "创建入库单", notes = "新建物资入库工单")
    @PostMapping("/create")
    public Result<Void> createInboundOrder(@RequestBody @Valid @ApiParam("入库单信息") InboundOrder inboundOrder) {
        boolean success = inboundService.createInboundOrder(inboundOrder);
        return ResultUtils.condition(success, "创建入库单成功", "创建入库单失败");
    }

    /**
     * 确认入库
     */
    @ApiOperation(value = "确认入库", notes = "确认物资入库操作，更新库存")
    @ApiImplicitParam(name = "id", value = "入库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PostMapping("/{id}/confirm")
    public Result<Void> confirmInbound(@PathVariable Long id) {
        boolean success = inboundService.confirmInbound(id);
        return ResultUtils.condition(success, "确认入库成功", "确认入库失败");
    }

    /**
     * 取消入库
     */
    @ApiOperation(value = "取消入库", notes = "取消入库单")
    @ApiImplicitParam(name = "id", value = "入库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PostMapping("/{id}/cancel")
    public Result<Void> cancelInbound(@PathVariable Long id) {
        boolean success = inboundService.cancelInbound(id);
        return ResultUtils.condition(success, "取消入库成功", "取消入库失败");
    }
}
