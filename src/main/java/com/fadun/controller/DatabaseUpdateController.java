package com.fadun.controller;

import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据库更新控制器（临时使用）
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "数据库更新", description = "临时数据库更新接口")
@RestController
@RequestMapping("/api/database")
@RequiredArgsConstructor
public class DatabaseUpdateController {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 更新数据库表结构和数据
     */
    @ApiOperation(value = "更新数据库", notes = "更新表结构并插入库存数据")
    @PostMapping("/update")
    public Result<String> updateDatabase() {
        try {
            // 删除冻结库存字段（如果存在）
            try {
                jdbcTemplate.execute("ALTER TABLE material_stock DROP COLUMN available_stock");
            } catch (Exception e) {
                // 字段可能不存在，忽略错误
            }
            
            try {
                jdbcTemplate.execute("ALTER TABLE material_stock DROP COLUMN frozen_stock");
            } catch (Exception e) {
                // 字段可能不存在，忽略错误
            }

            // 删除入库明细的批次号和存放位置字段
            try {
                jdbcTemplate.execute("ALTER TABLE inbound_detail DROP COLUMN batch_no");
            } catch (Exception e) {
                // 字段可能不存在，忽略错误
            }
            
            try {
                jdbcTemplate.execute("ALTER TABLE inbound_detail DROP COLUMN location");
            } catch (Exception e) {
                // 字段可能不存在，忽略错误
            }

            // 删除出库明细的批次号和存放位置字段
            try {
                jdbcTemplate.execute("ALTER TABLE outbound_detail DROP COLUMN batch_no");
            } catch (Exception e) {
                // 字段可能不存在，忽略错误
            }
            
            try {
                jdbcTemplate.execute("ALTER TABLE outbound_detail DROP COLUMN location");
            } catch (Exception e) {
                // 字段可能不存在，忽略错误
            }

            // 清空并重新插入物资库存数据
            jdbcTemplate.execute("DELETE FROM material_stock");

            // 插入物资库存数据
            String insertSql = "INSERT INTO material_stock (material_id, warehouse_id, current_stock, location, last_in_time, last_out_time, update_time) VALUES " +
                    "(1, 1, 40, 'A区-01架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'), " +
                    "(2, 1, 310, 'A区-02架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'), " +
                    "(3, 2, 8, 'B区-01架', '2024-01-05 11:00:00', '2024-01-12 09:30:00', '2024-01-12 09:30:00'), " +
                    "(4, 1, 3, 'C区-01架', NULL, NULL, '2024-01-01 00:00:00'), " +
                    "(5, 3, 90, 'D区-01架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'), " +
                    "(6, 3, 95, 'D区-02架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'), " +
                    "(7, 2, 12, 'B区-02架', NULL, NULL, '2024-01-01 00:00:00'), " +
                    "(8, 3, 25, 'D区-03架', NULL, NULL, '2024-01-01 00:00:00'), " +
                    "(9, 1, 120, 'A区-03架', NULL, NULL, '2024-01-01 00:00:00'), " +
                    "(10, 4, 15, 'E区-01架', '2024-01-20 14:00:00', NULL, '2024-01-20 14:00:00')";

            jdbcTemplate.execute(insertSql);

            return ResultUtils.success("数据库更新成功");
        } catch (Exception e) {
            return ResultUtils.fail("数据库更新失败: " + e.getMessage());
        }
    }
}
