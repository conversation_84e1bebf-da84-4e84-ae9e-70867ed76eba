package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.OutboundOrder;
import com.fadun.service.OutboundService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 物资出库管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-物资出库", description = "物资出库操作管理")
@RestController
@RequestMapping("/api/outbound")
@RequiredArgsConstructor
public class OutboundController {

    private final OutboundService outboundService;

    /**
     * 分页查询出库单
     */
    @ApiOperation(value = "分页查询出库单", notes = "获取出库单列表，支持按类型和状态筛选")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页码", defaultValue = "1", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "10", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（出库单号/接收单位/接收人）", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "outboundType", value = "出库类型", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态", dataTypeClass = Integer.class, paramType = "query")
    })
    @GetMapping("/page")
    public Result<PageResult<OutboundOrder>> getOutboundPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String outboundType,
            @RequestParam(required = false) Integer status) {
        
        PageResult<OutboundOrder> pageResult = outboundService.getOutboundPage(current, size, keyword, outboundType, status);
        return ResultUtils.page("查询出库单成功", pageResult);
    }

    /**
     * 获取出库单详情
     */
    @ApiOperation(value = "获取出库单详情", notes = "根据出库单ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "出库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/{id}")
    public Result<OutboundOrder> getOutboundDetail(@PathVariable Long id) {
        OutboundOrder outboundOrder = outboundService.getOutboundDetail(id);
        return ResultUtils.success("获取出库单详情成功", outboundOrder);
    }

    /**
     * 创建出库单
     */
    @ApiOperation(value = "创建出库单", notes = "新建物资出库工单")
    @PostMapping("/create")
    public Result<String> createOutboundOrder(@RequestBody @Valid @ApiParam("出库单信息") OutboundOrder outboundOrder) {
        return outboundService.createOutboundOrder(outboundOrder);
    }

    /**
     * 确认出库
     */
    @ApiOperation(value = "确认出库", notes = "确认物资出库操作，更新库存")
    @ApiImplicitParam(name = "id", value = "出库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PostMapping("/{id}/confirm")
    public Result<Void> confirmOutbound(@PathVariable Long id) {
        boolean success = outboundService.confirmOutbound(id);
        return ResultUtils.condition(success, "确认出库成功", "确认出库失败");
    }

    /**
     * 取消出库
     */
    @ApiOperation(value = "取消出库", notes = "取消出库单")
    @ApiImplicitParam(name = "id", value = "出库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PostMapping("/{id}/cancel")
    public Result<Void> cancelOutbound(@PathVariable Long id) {
        boolean success = outboundService.cancelOutbound(id);
        return ResultUtils.condition(success, "取消出库成功", "取消出库失败");
    }
}
