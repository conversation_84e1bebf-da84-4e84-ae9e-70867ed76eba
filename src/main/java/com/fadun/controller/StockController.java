package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.MaterialStock;
import com.fadun.service.StockService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 库存管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-库存管理", description = "物资库存查询和盘点管理")
@RestController
@RequestMapping("/api/stock")
@RequiredArgsConstructor
public class StockController {

    private final StockService stockService;

    /**
     * 分页查询库存信息
     */
    @ApiOperation(value = "分页查询库存信息", notes = "获取物资库存信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页码", defaultValue = "1", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "10", dataTypeClass = Long.class, paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词", dataTypeClass = String.class, paramType = "query"),
            @ApiImplicitParam(name = "warehouseId", value = "库室ID", dataTypeClass = Long.class, paramType = "query")
    })
    @GetMapping("/page")
    public Result<PageResult<MaterialStock>> getStockPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long warehouseId) {

        PageResult<MaterialStock> pageResult = stockService.getStockPage(current, size, keyword, warehouseId);
        return ResultUtils.page("查询库存信息成功", pageResult);
    }

    /**
     * 获取库存统计信息
     */
    @ApiOperation(value = "获取库存统计信息", notes = "获取库存总览和统计数据")
    @ApiImplicitParam(name = "warehouseId", value = "库室ID", dataType = "long", paramType = "query")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStockStatistics(@RequestParam(required = false) Long warehouseId) {
        Map<String, Object> statistics = stockService.getStockStatistics(warehouseId);
        return ResultUtils.success("获取库存统计成功", statistics);
    }



    /**
     * 创建盘点单
     */
    @ApiOperation(value = "创建盘点单", notes = "生成库存盘点工单")
    @PostMapping("/inventory/create")
    public Result<Void> createInventoryOrder(@RequestBody @ApiParam("盘点信息") Map<String, Object> inventoryInfo) {
        boolean success = stockService.createInventoryOrder(inventoryInfo);
        return ResultUtils.condition(success, "创建盘点单成功", "创建盘点单失败");
    }

    /**
     * 更新盘点结果
     */
    @ApiOperation(value = "更新盘点结果", notes = "录入实际盘点数量")
    @PostMapping("/inventory/update")
    public Result<Void> updateInventoryResult(@RequestBody @ApiParam("盘点结果") Map<String, Object> inventoryResult) {
        boolean success = stockService.updateInventoryResult(inventoryResult);
        return ResultUtils.condition(success, "更新盘点结果成功", "更新盘点结果失败");
    }

    /**
     * 完成盘点
     */
    @ApiOperation(value = "完成盘点", notes = "完成盘点并调整库存")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @PostMapping("/inventory/{orderId}/complete")
    public Result<Void> completeInventory(@PathVariable Long orderId) {
        boolean success = stockService.completeInventory(orderId);
        return ResultUtils.condition(success, "完成盘点成功", "完成盘点失败");
    }
}
