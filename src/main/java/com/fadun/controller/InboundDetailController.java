package com.fadun.controller;

import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.InboundDetail;
import com.fadun.service.InboundDetailService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 入库单明细管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-入库明细", description = "入库单明细管理")
@RestController
@RequestMapping("/api/inbound-detail")
@RequiredArgsConstructor
public class InboundDetailController {

    private final InboundDetailService inboundDetailService;

    /**
     * 根据入库单ID查询明细列表
     */
    @ApiOperation(value = "查询入库明细", notes = "根据入库单ID查询明细列表")
    @ApiImplicitParam(name = "orderId", value = "入库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @GetMapping("/order/{orderId}")
    public Result<List<InboundDetail>> getDetailsByOrderId(@PathVariable Long orderId) {
        List<InboundDetail> details = inboundDetailService.getDetailsByOrderId(orderId);
        return ResultUtils.success("查询入库明细成功", details);
    }

    /**
     * 批量保存入库明细
     */
    @ApiOperation(value = "批量保存明细", notes = "批量保存入库明细")
    @PostMapping("/batch")
    public Result<Void> saveDetails(@RequestBody @ApiParam("明细列表") List<InboundDetail> details) {
        boolean success = inboundDetailService.saveDetails(details);
        return ResultUtils.condition(success, "保存明细成功", "保存明细失败");
    }

    /**
     * 删除入库单的所有明细
     */
    @ApiOperation(value = "删除明细", notes = "删除入库单的所有明细")
    @ApiImplicitParam(name = "orderId", value = "入库单ID", required = true, dataTypeClass = Long.class, paramType = "path")
    @DeleteMapping("/order/{orderId}")
    public Result<Void> deleteByOrderId(@PathVariable Long orderId) {
        boolean success = inboundDetailService.deleteByOrderId(orderId);
        return ResultUtils.condition(success, "删除明细成功", "删除明细失败");
    }
}
