package com.fadun;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 * 法盾项目主启动类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
public class FadunApplication {

    private static final Logger log = LoggerFactory.getLogger(FadunApplication.class);

    @Value("${server.port:8080}")
    private String serverPort;

    public static void main(String[] args) {
        SpringApplication.run(FadunApplication.class, args);
    }

    /**
     * 应用启动完成后打印访问地址
     */
    @Bean
    public ApplicationRunner applicationRunner() {
        return args -> {
            String localIp = getLocalIpAddress();
            String contextPath = "/fadun";

            log.info("===========================================");
            log.info("🚀 应用启动成功！");
            log.info("📖 Knife4j接口文档地址:");
            log.info("   - 本地访问: http://localhost:{}{}/doc.html", serverPort, contextPath);
            if (localIp != null && !localIp.equals("127.0.0.1")) {
                log.info("   - 网络访问: http://{}:{}{}/doc.html", localIp, serverPort, contextPath);
            }
            log.info("===========================================");
        };
    }

    /**
     * 获取本机IP地址
     */
    private String getLocalIpAddress() {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();

                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();

                    // 只获取IPv4地址，排除回环地址
                    if (!inetAddress.isLoopbackAddress() &&
                        !inetAddress.isLinkLocalAddress() &&
                        inetAddress.getHostAddress().indexOf(':') == -1) {
                        return inetAddress.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取本机IP地址失败: {}", e.getMessage());
        }
        return null;
    }
}
