package com.fadun;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

/**
 * 法盾项目主启动类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
public class FadunApplication {

    private static final Logger log = LoggerFactory.getLogger(FadunApplication.class);

    @Value("${server.port:8080}")
    private String serverPort;

    public static void main(String[] args) {
        SpringApplication.run(FadunApplication.class, args);
    }

    /**
     * 应用启动完成后打印访问地址
     */
    @Bean
    public ApplicationRunner applicationRunner() {
        return args -> {
            log.info("===========================================");
            log.info("📖 Knife4j接口文档地址: http://localhost:{}/fadun/doc.html", serverPort);
            log.info("===========================================");
        };
    }
}
