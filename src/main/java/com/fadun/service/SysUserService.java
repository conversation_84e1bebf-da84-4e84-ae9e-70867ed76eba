package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.common.result.PageResult;
import com.fadun.entity.SysUser;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 分页查询用户列表
     * @param current 当前页
     * @param size 页大小
     * @param keyword 关键词（用户名、真实姓名、手机号）
     * @param roleType 角色类型
     * @param status 状态
     * @return 分页结果
     */
    PageResult<SysUser> getUserPage(Long current, Long size, String keyword, String roleType, Integer status);

    /**
     * 创建用户
     * @param user 用户信息
     * @return 是否成功
     */
    boolean createUser(SysUser user);

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 是否成功
     */
    boolean updateUser(SysUser user);

    /**
     * 删除用户
     * @param id 用户ID
     * @return 是否成功
     */
    boolean deleteUser(Long id);

    /**
     * 获取用户详情
     * @param id 用户ID
     * @return 用户信息
     */
    SysUser getUserDetail(Long id);

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsername(String username);

    /**
     * 重置用户密码
     * @param id 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean resetPassword(Long id, String newPassword);

    /**
     * 启用/禁用用户
     * @param id 用户ID
     * @param status 状态：1-启用，0-禁用
     * @return 是否成功
     */
    boolean updateUserStatus(Long id, Integer status);

    /**
     * 用户登录
     * @param username 用户名
     * @param password 密码
     * @return 用户信息（登录成功），null（登录失败）
     */
    SysUser login(String username, String password);

    /**
     * 检查用户权限
     * @param userId 用户ID
     * @param module 模块名称
     * @return 是否有权限
     */
    boolean checkUserPermission(Long userId, String module);
}
