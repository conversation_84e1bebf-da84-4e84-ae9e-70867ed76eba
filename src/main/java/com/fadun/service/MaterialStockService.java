package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.entity.MaterialStock;

import java.util.List;

/**
 * 物资库存服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MaterialStockService extends IService<MaterialStock> {

    /**
     * 入库操作 - 增加库存
     */
    boolean inboundStock(Long materialId, Long warehouseId, Integer quantity);

    /**
     * 出库操作 - 减少库存
     */
    boolean outboundStock(Long materialId, Long warehouseId, Integer quantity);

    /**
     * 检查库存是否充足
     */
    boolean checkStockSufficient(Long materialId, Long warehouseId, Integer quantity);

    /**
     * 获取或创建库存记录
     */
    MaterialStock getOrCreateStock(Long materialId, Long warehouseId);

    /**
     * 获取物资总库存信息（包含所有库房的库存汇总）
     */
    List<MaterialStock> getMaterialWithTotalStock();

    /**
     * 获取物资的总库存数量（所有库房的库存之和）
     */
    Integer getTotalStock(Long materialId);
}
