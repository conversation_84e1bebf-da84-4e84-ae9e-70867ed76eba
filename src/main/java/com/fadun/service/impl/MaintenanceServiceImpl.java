package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.result.PageResult;
import com.fadun.entity.MaintenanceRecord;
import com.fadun.mapper.MaintenanceRecordMapper;
import com.fadun.service.MaintenanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * 保养记录服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class MaintenanceServiceImpl extends ServiceImpl<MaintenanceRecordMapper, MaintenanceRecord> implements MaintenanceService {

    @Override
    public PageResult<MaintenanceRecord> getMaintenancePage(Long current, Long size, String keyword, Long materialId, String maintenanceType) {
        Page<MaintenanceRecord> page = new Page<>(current, size);
        LambdaQueryWrapper<MaintenanceRecord> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            // TODO: 关联查询物资信息进行模糊搜索
        }
        
        if (materialId != null) {
            wrapper.eq(MaintenanceRecord::getMaterialId, materialId);
        }
        
        if (StringUtils.hasText(maintenanceType)) {
            wrapper.eq(MaintenanceRecord::getMaintenanceType, maintenanceType);
        }
        
        wrapper.orderByDesc(MaintenanceRecord::getMaintenanceDate);
        
        Page<MaintenanceRecord> result = page(page, wrapper);
        return PageResult.fromPage(result);
    }

    @Override
    public boolean createMaintenanceRecord(MaintenanceRecord record) {
        // 设置保养日期
        if (record.getMaintenanceDate() == null) {
            record.setMaintenanceDate(LocalDate.now());
        }
        
        // 设置默认保养结果
        if (!StringUtils.hasText(record.getMaintenanceResult())) {
            record.setMaintenanceResult("normal");
        }
        
        return save(record);
    }

    @Override
    public List<MaintenanceRecord> getMaterialMaintenanceHistory(Long materialId) {
        LambdaQueryWrapper<MaintenanceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaintenanceRecord::getMaterialId, materialId)
                .orderByDesc(MaintenanceRecord::getMaintenanceDate);
        
        return list(wrapper);
    }

    @Override
    public List<MaintenanceRecord> getMaintenanceReminder() {
        // TODO: 实现保养提醒逻辑
        // 查询下次保养日期在今天之前或今天的记录
        LambdaQueryWrapper<MaintenanceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(MaintenanceRecord::getNextMaintenanceDate, LocalDate.now())
               .isNotNull(MaintenanceRecord::getNextMaintenanceDate)
               .orderByAsc(MaintenanceRecord::getNextMaintenanceDate);
        
        return list(wrapper);
    }
}
