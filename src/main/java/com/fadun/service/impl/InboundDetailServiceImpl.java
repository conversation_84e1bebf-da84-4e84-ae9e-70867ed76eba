package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.entity.InboundDetail;
import com.fadun.mapper.InboundDetailMapper;
import com.fadun.service.InboundDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 入库单明细服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class InboundDetailServiceImpl extends ServiceImpl<InboundDetailMapper, InboundDetail> implements InboundDetailService {

    @Override
    public List<InboundDetail> getDetailsByOrderId(Long orderId) {
        return baseMapper.selectDetailsByOrderId(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDetails(List<InboundDetail> details) {
        if (details == null || details.isEmpty()) {
            return true;
        }
        return saveBatch(details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByOrderId(Long orderId) {
        LambdaQueryWrapper<InboundDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InboundDetail::getOrderId, orderId);
        return remove(wrapper);
    }
}
