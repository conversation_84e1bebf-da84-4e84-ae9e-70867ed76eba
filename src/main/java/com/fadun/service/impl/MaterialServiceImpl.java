package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.exception.BusinessException;
import com.fadun.common.result.PageResult;
import com.fadun.entity.Material;
import com.fadun.mapper.MaterialMapper;
import com.fadun.service.MaterialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 物资信息服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    @Override
    public PageResult<Material> getMaterialPage(Long current, Long size, String keyword, Long categoryId, Long warehouseId) {
        Page<Material> page = new Page<>(current, size);
        Page<Material> result = baseMapper.selectMaterialPage(page, keyword, categoryId, warehouseId);
        return PageResult.fromPage(result);
    }

    @Override
    public boolean createMaterial(Material material) {
        // 自动生成物资编码
        if (material.getMaterialCode() == null || material.getMaterialCode().trim().isEmpty()) {
            material.setMaterialCode(generateMaterialCode());
        } else {
            // 如果手动输入了编码，检查是否重复
            LambdaQueryWrapper<Material> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Material::getMaterialCode, material.getMaterialCode());
            if (count(wrapper) > 0) {
                throw BusinessException.dataExists("物资编码已存在");
            }
        }

        // 设置默认状态
        if (material.getStatus() == null) {
            material.setStatus(1);
        }

        return save(material);
    }

    @Override
    public boolean updateMaterial(Material material) {
        // 检查物资是否存在
        Material existing = getById(material.getId());
        if (existing == null) {
            throw BusinessException.dataNotFound("物资不存在");
        }

        // 检查物资编码是否重复（排除自己）
        LambdaQueryWrapper<Material> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Material::getMaterialCode, material.getMaterialCode())
               .ne(Material::getId, material.getId());
        if (count(wrapper) > 0) {
            throw BusinessException.dataExists("物资编码已存在");
        }

        return updateById(material);
    }

    @Override
    public boolean deleteMaterial(Long id) {
        // 检查物资是否存在
        Material material = getById(id);
        if (material == null) {
            throw BusinessException.dataNotFound("物资不存在");
        }

        // TODO: 检查是否有库存记录，有库存的物资不能删除

        return removeById(id);
    }

    @Override
    public Material getMaterialDetail(Long id) {
        Material material = getById(id);
        if (material == null) {
            throw BusinessException.dataNotFound("物资不存在");
        }

        // TODO: 查询库存信息等扩展数据

        return material;
    }

    /**
     * 自动生成物资编码
     * 编码规则：MAT + 6位数字 (MAT000001, MAT000002, ...)
     */
    private String generateMaterialCode() {
        // 查找最大的物资编码
        LambdaQueryWrapper<Material> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Material::getMaterialCode, "MAT")
               .orderByDesc(Material::getMaterialCode)
               .last("LIMIT 1");

        Material lastMaterial = getOne(wrapper);

        int nextNumber = 1;
        if (lastMaterial != null && lastMaterial.getMaterialCode() != null) {
            String lastCode = lastMaterial.getMaterialCode();
            if (lastCode.startsWith("MAT") && lastCode.length() == 9) {
                try {
                    String numberPart = lastCode.substring(3); // 去掉"MAT"前缀
                    if (numberPart.matches("\\d{6}")) { // 确保是6位数字
                        int lastNumber = Integer.parseInt(numberPart);
                        nextNumber = lastNumber + 1;
                    }
                } catch (Exception e) {
                    // 解析失败，使用默认值1
                }
            }
        }

        // 生成新编码，确保序号不超过999999
        if (nextNumber > 999999) {
            throw BusinessException.paramError("物资数量已达上限(999999个)");
        }

        return "MAT" + String.format("%06d", nextNumber);
    }
}
