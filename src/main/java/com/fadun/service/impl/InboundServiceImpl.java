package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.exception.BusinessException;
import com.fadun.common.result.PageResult;
import com.fadun.entity.InboundOrder;
import com.fadun.entity.InboundDetail;
import com.fadun.mapper.InboundOrderMapper;
import com.fadun.service.InboundService;
import com.fadun.service.InboundDetailService;
import com.fadun.service.MaterialStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 入库管理服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InboundServiceImpl extends ServiceImpl<InboundOrderMapper, InboundOrder> implements InboundService {

    private final InboundDetailService inboundDetailService;
    private final MaterialStockService materialStockService;

    @Override
    public PageResult<InboundOrder> getInboundPage(Long current, Long size, String keyword, String inboundType, Integer status) {
        Page<InboundOrder> page = new Page<>(current, size);
        LambdaQueryWrapper<InboundOrder> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(InboundOrder::getOrderNo, keyword).or().like(InboundOrder::getSupplier, keyword));
        }

        if (StringUtils.hasText(inboundType)) {
            wrapper.eq(InboundOrder::getInboundType, inboundType);
        }

        if (status != null) {
            wrapper.eq(InboundOrder::getStatus, status);
        }

        wrapper.orderByDesc(InboundOrder::getCreateTime);

        Page<InboundOrder> result = page(page, wrapper);
        return PageResult.fromPage(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createInboundOrder(InboundOrder inboundOrder) {
        // 生成入库单号
        String orderNo = generateOrderNo();
        inboundOrder.setOrderNo(orderNo);

        // 设置默认状态
        if (inboundOrder.getStatus() == null) {
            inboundOrder.setStatus(1); // 待入库
        }

        // 设置入库时间
        if (inboundOrder.getInboundTime() == null) {
            inboundOrder.setInboundTime(LocalDateTime.now());
        }

        // 计算总数量
        if (inboundOrder.getDetails() != null && !inboundOrder.getDetails().isEmpty()) {
            int totalQuantity = inboundOrder.getDetails().stream()
                    .mapToInt(detail -> detail.getQuantity() != null ? detail.getQuantity() : 0)
                    .sum();
            inboundOrder.setTotalQuantity(totalQuantity);
        }

        // 保存入库单
        boolean success = save(inboundOrder);

        // 保存入库明细并更新库存
        if (success && inboundOrder.getDetails() != null && !inboundOrder.getDetails().isEmpty()) {
            // 设置入库单ID
            inboundOrder.getDetails().forEach(detail -> detail.setOrderId(inboundOrder.getId()));
            success = inboundDetailService.saveDetails(inboundOrder.getDetails());

            // 更新库存
            if (success) {
                for (InboundDetail detail : inboundOrder.getDetails()) {
                    boolean stockResult = materialStockService.inboundStock(
                        detail.getMaterialId(),
                        inboundOrder.getWarehouseId(),
                        detail.getQuantity()
                    );
                    if (!stockResult) {
                        throw new RuntimeException("更新库存失败：物资ID=" + detail.getMaterialId());
                    }
                }
            }
        }

        return success;
    }

    @Override
    public boolean confirmInbound(Long orderId) {
        InboundOrder order = getById(orderId);
        if (order == null) {
            throw BusinessException.dataNotFound("入库单不存在");
        }

        if (order.getStatus() != 1) {
            throw BusinessException.of("只有待入库状态的单据才能确认入库");
        }

        // 更新状态为已入库
        order.setStatus(2);
        boolean success = updateById(order);

        if (success) {
            // TODO: 更新库存
            log.info("入库单 {} 确认入库成功", order.getOrderNo());
        }

        return success;
    }

    @Override
    public boolean cancelInbound(Long orderId) {
        InboundOrder order = getById(orderId);
        if (order == null) {
            throw BusinessException.dataNotFound("入库单不存在");
        }

        if (order.getStatus() == 2) {
            throw BusinessException.of("已入库的单据不能取消");
        }

        // 更新状态为已取消
        order.setStatus(3);
        return updateById(order);
    }

    @Override
    public InboundOrder getInboundDetail(Long orderId) {
        InboundOrder order = getById(orderId);
        if (order == null) {
            throw BusinessException.dataNotFound("入库单不存在");
        }

        // 查询入库明细
        order.setDetails(inboundDetailService.getDetailsByOrderId(orderId));

        return order;
    }

    /**
     * 生成入库单号
     */
    private String generateOrderNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // TODO: 实现更完善的单号生成逻辑
        return "IN" + dateStr + System.currentTimeMillis() % 10000;
    }
}
