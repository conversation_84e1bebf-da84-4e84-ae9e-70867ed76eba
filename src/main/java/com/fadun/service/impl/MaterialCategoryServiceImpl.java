package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.exception.BusinessException;
import com.fadun.entity.MaterialCategory;
import com.fadun.mapper.MaterialCategoryMapper;
import com.fadun.service.MaterialCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物资分类服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class MaterialCategoryServiceImpl extends ServiceImpl<MaterialCategoryMapper, MaterialCategory> implements MaterialCategoryService {

    @Override
    public List<MaterialCategory> getCategoryTree() {
        // 查询所有分类，按编码排序
        List<MaterialCategory> allCategories = list(
            new LambdaQueryWrapper<MaterialCategory>()
                .orderByAsc(MaterialCategory::getCategoryCode)
        );

        // 构建树形结构
        return buildCategoryTree(allCategories, 0L);
    }

    @Override
    public List<MaterialCategory> getCategoryList() {
        return list(
            new LambdaQueryWrapper<MaterialCategory>()
                .orderByAsc(MaterialCategory::getCategoryCode)
        );
    }

    @Override
    public boolean createCategory(MaterialCategory category) {
        // 设置默认值
        if (category.getParentId() == null) {
            category.setParentId(0L);
        }

        // 计算层级
        if (category.getLevel() == null) {
            if (category.getParentId() == 0) {
                category.setLevel(1);
            } else {
                MaterialCategory parent = getById(category.getParentId());
                category.setLevel(parent.getLevel() + 1);
            }
        }

        // 自动生成分类编码
        if (category.getCategoryCode() == null || category.getCategoryCode().trim().isEmpty()) {
            category.setCategoryCode(generateCategoryCode(category.getParentId(), category.getLevel()));
        } else {
            // 如果手动输入了编码，检查是否重复
            if (existsCategoryCode(category.getCategoryCode(), null)) {
                throw BusinessException.dataExists("分类编码已存在");
            }
        }

        return save(category);
    }

    @Override
    public boolean updateCategory(MaterialCategory category) {
        // 检查分类是否存在
        MaterialCategory existing = getById(category.getId());
        if (existing == null) {
            throw BusinessException.dataNotFound("分类不存在");
        }

        // 检查分类编码是否重复（排除自己）
        if (existsCategoryCode(category.getCategoryCode(), category.getId())) {
            throw BusinessException.dataExists("分类编码已存在");
        }

        return updateById(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long id) {
        // 检查分类是否存在
        MaterialCategory category = getById(id);
        if (category == null) {
            throw BusinessException.dataNotFound("分类不存在");
        }

        // 检查是否有子分类
        int childrenCount = baseMapper.countChildrenByCategory(id);
        if (childrenCount > 0) {
            throw BusinessException.of("该分类下存在子分类，无法删除");
        }

        // 检查是否有物资使用该分类
        int materialCount = baseMapper.countMaterialsByCategory(id);
        if (materialCount > 0) {
            throw BusinessException.of("该分类下存在物资，无法删除");
        }

        return removeById(id);
    }

    @Override
    public List<MaterialCategory> getChildrenCategories(Long parentId) {
        return list(
            new LambdaQueryWrapper<MaterialCategory>()
                .eq(MaterialCategory::getParentId, parentId)
                .orderByAsc(MaterialCategory::getCategoryName)
        );
    }

    @Override
    public boolean existsCategoryCode(String categoryCode, Long excludeId) {
        LambdaQueryWrapper<MaterialCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialCategory::getCategoryCode, categoryCode);
        if (excludeId != null) {
            wrapper.ne(MaterialCategory::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    /**
     * 自动生成分类编码
     * 编码规则：
     * 一级分类：CAT + 2位数字 (CAT01, CAT02, ...)
     * 二级分类：父级编码 + 2位数字 (CAT0101, CAT0102, ...)
     * 三级分类：父级编码 + 2位数字 (CAT010101, CAT010102, ...)
     */
    private String generateCategoryCode(Long parentId, Integer level) {
        String baseCode;
        String codePrefix;

        if (parentId == 0) {
            // 一级分类
            baseCode = "CAT";
            codePrefix = "CAT";
        } else {
            // 二级或三级分类，获取父级编码
            MaterialCategory parent = getById(parentId);
            if (parent == null) {
                throw BusinessException.dataNotFound("父级分类不存在");
            }
            baseCode = parent.getCategoryCode();
            codePrefix = baseCode;
        }

        // 查找同级别的最大编码序号
        LambdaQueryWrapper<MaterialCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialCategory::getParentId, parentId)
               .like(MaterialCategory::getCategoryCode, codePrefix)
               .orderByDesc(MaterialCategory::getCategoryCode);

        List<MaterialCategory> siblings = list(wrapper);

        int nextNumber = 1;
        if (!siblings.isEmpty()) {
            // 从现有编码中提取最大序号
            for (MaterialCategory sibling : siblings) {
                String siblingCode = sibling.getCategoryCode();
                if (siblingCode.startsWith(codePrefix)) {
                    try {
                        // 提取编码末尾的数字部分
                        String numberPart = siblingCode.substring(codePrefix.length());
                        if (numberPart.matches("\\d{2}")) {  // 确保是2位数字
                            int number = Integer.parseInt(numberPart);
                            if (number >= nextNumber) {
                                nextNumber = number + 1;
                            }
                        }
                    } catch (Exception e) {
                        // 忽略解析错误，继续处理
                    }
                }
            }
        }

        // 生成新编码，确保序号不超过99
        if (nextNumber > 99) {
            throw BusinessException.paramError("同级分类数量已达上限(99个)");
        }

        return baseCode + String.format("%02d", nextNumber);
    }

    /**
     * 构建分类树形结构
     */
    private List<MaterialCategory> buildCategoryTree(List<MaterialCategory> allCategories, Long parentId) {
        List<MaterialCategory> result = new ArrayList<>();

        for (MaterialCategory category : allCategories) {
            if (parentId.equals(category.getParentId())) {
                // 递归查找子分类
                List<MaterialCategory> children = buildCategoryTree(allCategories, category.getId());
                category.setChildren(children);
                result.add(category);
            }
        }

        return result;
    }


}
