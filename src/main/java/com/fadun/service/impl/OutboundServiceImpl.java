package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.exception.BusinessException;
import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.OutboundOrder;
import com.fadun.entity.OutboundDetail;
import com.fadun.entity.MaterialStock;
import com.fadun.mapper.OutboundOrderMapper;
import com.fadun.service.OutboundService;
import com.fadun.service.OutboundDetailService;
import com.fadun.service.MaterialStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 出库管理服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OutboundServiceImpl extends ServiceImpl<OutboundOrderMapper, OutboundOrder> implements OutboundService {

    private final OutboundDetailService outboundDetailService;
    private final MaterialStockService materialStockService;

    @Override
    public PageResult<OutboundOrder> getOutboundPage(Long current, Long size, String keyword, String outboundType, Integer status) {
        Page<OutboundOrder> page = new Page<>(current, size);
        LambdaQueryWrapper<OutboundOrder> wrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(OutboundOrder::getOrderNo, keyword)
                           .or().like(OutboundOrder::getReceiverUnit, keyword)
                           .or().like(OutboundOrder::getReceiverName, keyword));
        }
        
        if (StringUtils.hasText(outboundType)) {
            wrapper.eq(OutboundOrder::getOutboundType, outboundType);
        }
        
        if (status != null) {
            wrapper.eq(OutboundOrder::getStatus, status);
        }
        
        wrapper.orderByDesc(OutboundOrder::getCreateTime);
        
        Page<OutboundOrder> result = page(page, wrapper);
        return PageResult.fromPage(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createOutboundOrder(OutboundOrder outboundOrder) {
        // 生成出库单号
        String orderNo = generateOrderNo();
        outboundOrder.setOrderNo(orderNo);

        // 设置默认状态
        if (outboundOrder.getStatus() == null) {
            outboundOrder.setStatus(1); // 待出库
        }

        // 设置出库时间
        if (outboundOrder.getOutboundTime() == null) {
            outboundOrder.setOutboundTime(LocalDateTime.now());
        }

        // 先检查库存，避免无效的数据库操作
        if (outboundOrder.getDetails() != null && !outboundOrder.getDetails().isEmpty()) {
            // 检查所有物资的库存是否充足
            for (OutboundDetail detail : outboundOrder.getDetails()) {
                boolean sufficient = materialStockService.checkStockSufficient(
                    detail.getMaterialId(),
                    outboundOrder.getWarehouseId(),
                    detail.getQuantity()
                );
                if (!sufficient) {
                    // 获取当前库房库存和总库存数量
                    Integer totalStock = materialStockService.getTotalStock(detail.getMaterialId());

                    // 获取指定库房的库存
                    MaterialStock warehouseStock = materialStockService.getOrCreateStock(
                        detail.getMaterialId(), outboundOrder.getWarehouseId());
                    Integer currentWarehouseStock = warehouseStock != null ? warehouseStock.getCurrentStock() : 0;

                    String errorMessage = "当前库房库存" + currentWarehouseStock +
                                        "，总库存" + (totalStock != null ? totalStock : 0) +
                                        "，出库数量" + detail.getQuantity() +
                                        "大于当前库房库存量，请修改数量！";

                    return ResultUtils.fail(errorMessage);
                }
            }

            // 计算总数量
            int totalQuantity = outboundOrder.getDetails().stream()
                    .mapToInt(detail -> detail.getQuantity() != null ? detail.getQuantity() : 0)
                    .sum();
            outboundOrder.setTotalQuantity(totalQuantity);
        }

        // 库存检查通过，开始保存数据
        // 保存出库单
        boolean success = save(outboundOrder);

        // 保存出库明细
        if (success && outboundOrder.getDetails() != null && !outboundOrder.getDetails().isEmpty()) {

            // 设置出库单ID
            outboundOrder.getDetails().forEach(detail -> detail.setOrderId(outboundOrder.getId()));
            success = outboundDetailService.saveDetails(outboundOrder.getDetails());

            // 更新库存
            if (success) {
                for (OutboundDetail detail : outboundOrder.getDetails()) {
                    boolean stockResult = materialStockService.outboundStock(
                        detail.getMaterialId(),
                        outboundOrder.getWarehouseId(),
                        detail.getQuantity()
                    );
                    if (!stockResult) {
                        throw new RuntimeException("更新库存失败：物资ID=" + detail.getMaterialId());
                    }
                }
            }
        }

        return success ? ResultUtils.success("出库单创建成功") : ResultUtils.fail("出库单创建失败");
    }

    @Override
    public boolean confirmOutbound(Long orderId) {
        OutboundOrder order = getById(orderId);
        if (order == null) {
            throw BusinessException.dataNotFound("出库单不存在");
        }
        
        if (order.getStatus() != 1) {
            throw BusinessException.of("只有待出库状态的单据才能确认出库");
        }
        
        // TODO: 检查库存是否充足
        
        // 更新状态为已出库
        order.setStatus(2);
        boolean success = updateById(order);
        
        if (success) {
            // TODO: 更新库存（减少）
            log.info("出库单 {} 确认出库成功", order.getOrderNo());
        }
        
        return success;
    }

    @Override
    public boolean cancelOutbound(Long orderId) {
        OutboundOrder order = getById(orderId);
        if (order == null) {
            throw BusinessException.dataNotFound("出库单不存在");
        }
        
        if (order.getStatus() == 2) {
            throw BusinessException.of("已出库的单据不能取消");
        }
        
        // 更新状态为已取消
        order.setStatus(3);
        return updateById(order);
    }

    @Override
    public OutboundOrder getOutboundDetail(Long orderId) {
        OutboundOrder order = getById(orderId);
        if (order == null) {
            throw BusinessException.dataNotFound("出库单不存在");
        }

        // 查询出库明细
        order.setDetails(outboundDetailService.getDetailsByOrderId(orderId));

        return order;
    }

    /**
     * 生成出库单号
     */
    private String generateOrderNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // TODO: 实现更完善的单号生成逻辑
        return "OUT" + dateStr + System.currentTimeMillis() % 10000;
    }
}
