package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.entity.OutboundDetail;
import com.fadun.mapper.OutboundDetailMapper;
import com.fadun.service.OutboundDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 出库单明细服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class OutboundDetailServiceImpl extends ServiceImpl<OutboundDetailMapper, OutboundDetail> implements OutboundDetailService {

    @Override
    public List<OutboundDetail> getDetailsByOrderId(Long orderId) {
        return baseMapper.selectDetailsByOrderId(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDetails(List<OutboundDetail> details) {
        if (details == null || details.isEmpty()) {
            return true;
        }
        return saveBatch(details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByOrderId(Long orderId) {
        LambdaQueryWrapper<OutboundDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OutboundDetail::getOrderId, orderId);
        return remove(wrapper);
    }
}
