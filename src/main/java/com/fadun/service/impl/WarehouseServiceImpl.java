package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.exception.BusinessException;
import com.fadun.entity.Warehouse;
import com.fadun.mapper.WarehouseMapper;
import com.fadun.service.WarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 库室信息服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class WarehouseServiceImpl extends ServiceImpl<WarehouseMapper, Warehouse> implements WarehouseService {

    @Override
    public IPage<Warehouse> getWarehousePage(Page<Warehouse> page, String keyword, Integer status) {
        LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<>();
        
        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Warehouse::getWarehouseName, keyword)
                           .or().like(Warehouse::getWarehouseCode, keyword)
                           .or().like(Warehouse::getLocation, keyword));
        }
        
        // 状态筛选
        if (status != null) {
            wrapper.eq(Warehouse::getStatus, status);
        }

        // 按库室名称排序
        wrapper.orderByAsc(Warehouse::getWarehouseName);

        return page(page, wrapper);
    }

    @Override
    public List<Warehouse> getWarehouseList() {
        return list(
            new LambdaQueryWrapper<Warehouse>()
                .eq(Warehouse::getStatus, 1)
                .orderByAsc(Warehouse::getWarehouseName)
        );
    }

    @Override
    public boolean createWarehouse(Warehouse warehouse) {
        // 检查库室编码是否重复
        if (existsWarehouseCode(warehouse.getWarehouseCode(), null)) {
            throw BusinessException.dataExists("库室编码已存在");
        }

        // 设置默认值
        if (warehouse.getStatus() == null) {
            warehouse.setStatus(1);
        }

        return save(warehouse);
    }

    @Override
    public boolean updateWarehouse(Warehouse warehouse) {
        // 检查库室是否存在
        Warehouse existing = getById(warehouse.getId());
        if (existing == null) {
            throw BusinessException.dataNotFound("库室不存在");
        }

        // 检查库室编码是否重复（排除自己）
        if (existsWarehouseCode(warehouse.getWarehouseCode(), warehouse.getId())) {
            throw BusinessException.dataExists("库室编码已存在");
        }

        return updateById(warehouse);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWarehouse(Long id) {
        // 检查库室是否存在
        Warehouse warehouse = getById(id);
        if (warehouse == null) {
            throw BusinessException.dataNotFound("库室不存在");
        }

        // 检查是否有物资使用该库室
        int materialCount = baseMapper.countMaterialsByWarehouse(id);
        if (materialCount > 0) {
            throw BusinessException.of("该库室下存在物资，无法删除");
        }

        return removeById(id);
    }

    @Override
    public boolean existsWarehouseCode(String warehouseCode, Long excludeId) {
        int count = baseMapper.countByWarehouseCode(warehouseCode, excludeId);
        return count > 0;
    }

    @Override
    public List<Warehouse> getWarehouseWithMaterialCount() {
        return baseMapper.selectWarehouseWithMaterialCount();
    }
}
