package com.fadun.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.entity.MaterialStock;
import com.fadun.mapper.MaterialStockMapper;
import com.fadun.service.MaterialStockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物资库存服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class MaterialStockServiceImpl extends ServiceImpl<MaterialStockMapper, MaterialStock> implements MaterialStockService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inboundStock(Long materialId, Long warehouseId, Integer quantity) {
        // 获取或创建库存记录
        MaterialStock stock = getOrCreateStock(materialId, warehouseId);
        
        // 增加库存
        int result = baseMapper.increaseStock(materialId, warehouseId, quantity);
        
        if (result == 0) {
            log.error("入库失败：物资ID={}, 库房ID={}, 数量={}", materialId, warehouseId, quantity);
            return false;
        }
        
        log.info("入库成功：物资ID={}, 库房ID={}, 数量={}", materialId, warehouseId, quantity);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean outboundStock(Long materialId, Long warehouseId, Integer quantity) {
        // 检查库存是否充足
        if (!checkStockSufficient(materialId, warehouseId, quantity)) {
            log.error("出库失败：库存不足，物资ID={}, 库房ID={}, 需要数量={}", materialId, warehouseId, quantity);
            return false;
        }
        
        // 减少库存
        int result = baseMapper.decreaseStock(materialId, warehouseId, quantity);
        
        if (result == 0) {
            log.error("出库失败：物资ID={}, 库房ID={}, 数量={}", materialId, warehouseId, quantity);
            return false;
        }
        
        log.info("出库成功：物资ID={}, 库房ID={}, 数量={}", materialId, warehouseId, quantity);
        return true;
    }

    @Override
    public boolean checkStockSufficient(Long materialId, Long warehouseId, Integer quantity) {
        // 检查指定库房的库存是否充足
        LambdaQueryWrapper<MaterialStock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialStock::getMaterialId, materialId)
               .eq(MaterialStock::getWarehouseId, warehouseId);

        MaterialStock stock = getOne(wrapper);
        if (stock == null) {
            return false;
        }

        return stock.getCurrentStock() >= quantity;
    }

    /**
     * 获取物资的总库存数量（所有库房的库存之和）
     */
    public Integer getTotalStock(Long materialId) {
        LambdaQueryWrapper<MaterialStock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialStock::getMaterialId, materialId);

        List<MaterialStock> stocks = list(wrapper);
        if (stocks == null || stocks.isEmpty()) {
            return 0;
        }

        return stocks.stream()
                .mapToInt(stock -> stock.getCurrentStock() != null ? stock.getCurrentStock() : 0)
                .sum();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialStock getOrCreateStock(Long materialId, Long warehouseId) {
        LambdaQueryWrapper<MaterialStock> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialStock::getMaterialId, materialId)
               .eq(MaterialStock::getWarehouseId, warehouseId);
        
        MaterialStock stock = getOne(wrapper);
        
        if (stock == null) {
            // 创建新的库存记录
            stock = new MaterialStock();
            stock.setMaterialId(materialId);
            stock.setWarehouseId(warehouseId);
            stock.setCurrentStock(0);
            stock.setUpdateTime(LocalDateTime.now());
            
            save(stock);
            log.info("创建新库存记录：物资ID={}, 库房ID={}", materialId, warehouseId);
        }
        
        return stock;
    }

    @Override
    public List<MaterialStock> getMaterialWithTotalStock() {
        return baseMapper.selectMaterialWithTotalStock();
    }
}
