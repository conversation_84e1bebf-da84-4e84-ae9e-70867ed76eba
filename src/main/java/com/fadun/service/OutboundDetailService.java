package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.entity.OutboundDetail;

import java.util.List;

/**
 * 出库单明细服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface OutboundDetailService extends IService<OutboundDetail> {

    /**
     * 根据出库单ID查询明细列表
     */
    List<OutboundDetail> getDetailsByOrderId(Long orderId);

    /**
     * 批量保存出库明细
     */
    boolean saveDetails(List<OutboundDetail> details);

    /**
     * 删除出库单的所有明细
     */
    boolean deleteByOrderId(Long orderId);
}
