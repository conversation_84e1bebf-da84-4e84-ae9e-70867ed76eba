package com.fadun.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.entity.Warehouse;

import java.util.List;

/**
 * 库室信息服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WarehouseService extends IService<Warehouse> {

    /**
     * 分页查询库室信息
     */
    IPage<Warehouse> getWarehousePage(Page<Warehouse> page, String keyword, Integer status);

    /**
     * 获取库室列表
     */
    List<Warehouse> getWarehouseList();

    /**
     * 创建库室
     */
    boolean createWarehouse(Warehouse warehouse);

    /**
     * 更新库室
     */
    boolean updateWarehouse(Warehouse warehouse);

    /**
     * 删除库室
     */
    boolean deleteWarehouse(Long id);

    /**
     * 检查库室编码是否存在
     */
    boolean existsWarehouseCode(String warehouseCode, Long excludeId);

    /**
     * 获取库室及其物资数量
     */
    List<Warehouse> getWarehouseWithMaterialCount();
}
