package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.entity.InboundDetail;

import java.util.List;

/**
 * 入库单明细服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InboundDetailService extends IService<InboundDetail> {

    /**
     * 根据入库单ID查询明细列表
     */
    List<InboundDetail> getDetailsByOrderId(Long orderId);

    /**
     * 批量保存入库明细
     */
    boolean saveDetails(List<InboundDetail> details);

    /**
     * 删除入库单的所有明细
     */
    boolean deleteByOrderId(Long orderId);
}
