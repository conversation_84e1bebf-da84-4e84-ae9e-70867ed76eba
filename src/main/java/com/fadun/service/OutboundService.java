package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.entity.OutboundOrder;

/**
 * 出库管理服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface OutboundService extends IService<OutboundOrder> {

    /**
     * 分页查询出库单
     */
    PageResult<OutboundOrder> getOutboundPage(Long current, Long size, String keyword, String outboundType, Integer status);

    /**
     * 创建出库单
     */
    Result<String> createOutboundOrder(OutboundOrder outboundOrder);

    /**
     * 确认出库
     */
    boolean confirmOutbound(Long orderId);

    /**
     * 取消出库
     */
    boolean cancelOutbound(Long orderId);

    /**
     * 获取出库单详情
     */
    OutboundOrder getOutboundDetail(Long orderId);
}
