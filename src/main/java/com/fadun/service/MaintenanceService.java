package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.common.result.PageResult;
import com.fadun.entity.MaintenanceRecord;

import java.util.List;

/**
 * 保养记录服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MaintenanceService extends IService<MaintenanceRecord> {

    /**
     * 分页查询保养记录
     */
    PageResult<MaintenanceRecord> getMaintenancePage(Long current, Long size, String keyword, Long materialId, String maintenanceType);

    /**
     * 创建保养记录
     */
    boolean createMaintenanceRecord(MaintenanceRecord record);

    /**
     * 获取物资保养历史
     */
    List<MaintenanceRecord> getMaterialMaintenanceHistory(Long materialId);

    /**
     * 获取保养提醒
     */
    List<MaintenanceRecord> getMaintenanceReminder();
}
