package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.common.result.PageResult;
import com.fadun.entity.Material;

/**
 * 物资信息服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MaterialService extends IService<Material> {

    /**
     * 分页查询物资台账
     */
    PageResult<Material> getMaterialPage(Long current, Long size, String keyword, Long categoryId, Long warehouseId);

    /**
     * 创建物资信息
     */
    boolean createMaterial(Material material);

    /**
     * 更新物资信息
     */
    boolean updateMaterial(Material material);

    /**
     * 删除物资信息
     */
    boolean deleteMaterial(Long id);

    /**
     * 获取物资详情
     */
    Material getMaterialDetail(Long id);
}
