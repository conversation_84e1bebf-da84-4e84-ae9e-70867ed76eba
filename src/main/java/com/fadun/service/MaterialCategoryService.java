package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.entity.MaterialCategory;

import java.util.List;

/**
 * 物资分类服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MaterialCategoryService extends IService<MaterialCategory> {

    /**
     * 获取分类树形结构
     */
    List<MaterialCategory> getCategoryTree();

    /**
     * 获取分类列表（按层级和排序）
     */
    List<MaterialCategory> getCategoryList();

    /**
     * 创建分类
     */
    boolean createCategory(MaterialCategory category);

    /**
     * 更新分类
     */
    boolean updateCategory(MaterialCategory category);

    /**
     * 删除分类
     */
    boolean deleteCategory(Long id);

    /**
     * 获取子分类列表
     */
    List<MaterialCategory> getChildrenCategories(Long parentId);

    /**
     * 检查分类编码是否存在
     */
    boolean existsCategoryCode(String categoryCode, Long excludeId);
}
