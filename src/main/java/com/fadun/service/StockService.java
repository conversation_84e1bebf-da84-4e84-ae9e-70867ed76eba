package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.common.result.PageResult;
import com.fadun.entity.MaterialStock;

import java.util.List;
import java.util.Map;

/**
 * 库存管理服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface StockService extends IService<MaterialStock> {

    /**
     * 分页查询库存信息
     */
    PageResult<MaterialStock> getStockPage(Long current, Long size, String keyword, Long warehouseId);

    /**
     * 获取库存统计信息
     */
    Map<String, Object> getStockStatistics(Long warehouseId);



    /**
     * 创建盘点单
     */
    boolean createInventoryOrder(Map<String, Object> inventoryInfo);

    /**
     * 更新盘点结果
     */
    boolean updateInventoryResult(Map<String, Object> inventoryResult);

    /**
     * 完成盘点
     */
    boolean completeInventory(Long orderId);

    /**
     * 更新库存
     */
    boolean updateStock(Long materialId, Long warehouseId, Integer quantity, String type);
}
