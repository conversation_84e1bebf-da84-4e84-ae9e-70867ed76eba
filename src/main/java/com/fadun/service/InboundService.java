package com.fadun.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fadun.common.result.PageResult;
import com.fadun.entity.InboundOrder;

/**
 * 入库管理服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InboundService extends IService<InboundOrder> {

    /**
     * 分页查询入库单
     */
    PageResult<InboundOrder> getInboundPage(Long current, Long size, String keyword, String inboundType, Integer status);

    /**
     * 创建入库单
     */
    boolean createInboundOrder(InboundOrder inboundOrder);

    /**
     * 确认入库
     */
    boolean confirmInbound(Long orderId);

    /**
     * 取消入库
     */
    boolean cancelInbound(Long orderId);

    /**
     * 获取入库单详情
     */
    InboundOrder getInboundDetail(Long orderId);
}
