package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统用户Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 分页查询用户列表
     * @param page 分页参数
     * @param keyword 关键词
     * @param roleType 角色类型
     * @param status 状态
     * @return 分页结果
     */
    Page<SysUser> selectUserPage(Page<SysUser> page, 
                                @Param("keyword") String keyword, 
                                @Param("roleType") String roleType, 
                                @Param("status") Integer status);
}
