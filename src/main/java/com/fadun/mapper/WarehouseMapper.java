package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fadun.entity.Warehouse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库室信息Mapper接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface WarehouseMapper extends BaseMapper<Warehouse> {

    /**
     * 查询库室及其物资数量
     */
    List<Warehouse> selectWarehouseWithMaterialCount();

    /**
     * 检查库室下是否有物资
     */
    int countMaterialsByWarehouse(@Param("warehouseId") Long warehouseId);

    /**
     * 检查库室编码是否存在
     */
    int countByWarehouseCode(@Param("warehouseCode") String warehouseCode, @Param("excludeId") Long excludeId);
}
