package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.MaterialStock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

import java.util.List;

/**
 * 物资库存Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface MaterialStockMapper extends BaseMapper<MaterialStock> {

    /**
     * 分页查询库存信息
     */
    Page<MaterialStock> selectStockPage(Page<MaterialStock> page,
                                        @Param("keyword") String keyword,
                                        @Param("warehouseId") Long warehouseId,
                                        @Param("lowStock") Boolean lowStock);

    /**
     * 获取库存统计信息
     */
    List<MaterialStock> selectStockStatistics(@Param("warehouseId") Long warehouseId);

    /**
     * 更新库存数量
     */
    int updateStock(@Param("materialId") Long materialId,
                    @Param("warehouseId") Long warehouseId,
                    @Param("quantity") Integer quantity,
                    @Param("type") String type);

    /**
     * 增加库存
     */
    @Update("UPDATE material_stock SET current_stock = current_stock + #{quantity}, " +
            "last_in_time = NOW(), update_time = NOW() " +
            "WHERE material_id = #{materialId} AND warehouse_id = #{warehouseId}")
    int increaseStock(@Param("materialId") Long materialId,
                     @Param("warehouseId") Long warehouseId,
                     @Param("quantity") Integer quantity);

    /**
     * 减少库存
     */
    @Update("UPDATE material_stock SET current_stock = current_stock - #{quantity}, " +
            "last_out_time = NOW(), update_time = NOW() " +
            "WHERE material_id = #{materialId} AND warehouse_id = #{warehouseId} " +
            "AND current_stock >= #{quantity}")
    int decreaseStock(@Param("materialId") Long materialId,
                     @Param("warehouseId") Long warehouseId,
                     @Param("quantity") Integer quantity);

    /**
     * 查询物资总库存（所有库房的库存之和）
     */
    @Select("SELECT m.id as materialId, m.material_code as materialCode, " +
            "m.material_name as materialName, m.specification, m.unit, " +
            "m.category_id as categoryId, c.category_name as categoryName, " +
            "COALESCE(SUM(s.current_stock), 0) as totalStock " +
            "FROM material m " +
            "LEFT JOIN material_stock s ON m.id = s.material_id " +
            "LEFT JOIN material_category c ON m.category_id = c.id " +
            "WHERE m.status = 1 " +
            "GROUP BY m.id, m.material_code, m.material_name, m.specification, m.unit, m.category_id, c.category_name " +
            "ORDER BY m.create_time DESC")
    List<MaterialStock> selectMaterialWithTotalStock();
}
