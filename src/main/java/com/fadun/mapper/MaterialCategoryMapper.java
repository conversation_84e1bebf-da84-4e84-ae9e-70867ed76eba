package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fadun.entity.MaterialCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物资分类Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface MaterialCategoryMapper extends BaseMapper<MaterialCategory> {

    /**
     * 查询分类树形结构
     */
    List<MaterialCategory> selectCategoryTree(@Param("parentId") Long parentId);

    /**
     * 查询分类及其物资数量
     */
    List<MaterialCategory> selectCategoryWithMaterialCount();

    /**
     * 检查分类下是否有物资
     */
    int countMaterialsByCategory(@Param("categoryId") Long categoryId);

    /**
     * 检查分类下是否有子分类
     */
    int countChildrenByCategory(@Param("categoryId") Long categoryId);
}
