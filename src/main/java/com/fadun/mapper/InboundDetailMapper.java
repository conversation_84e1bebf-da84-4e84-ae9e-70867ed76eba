package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fadun.entity.InboundDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 入库单明细Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface InboundDetailMapper extends BaseMapper<InboundDetail> {

    /**
     * 根据入库单ID查询明细列表（包含物资信息）
     */
    @Select("SELECT d.*, m.material_code, m.material_name, m.specification, m.unit " +
            "FROM inbound_detail d " +
            "LEFT JOIN material m ON d.material_id = m.id " +
            "WHERE d.order_id = #{orderId}")
    List<InboundDetail> selectDetailsByOrderId(@Param("orderId") Long orderId);
}
