package com.fadun.common.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页返回结果
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(description = "分页返回结果")
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Long current;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Long size;

    @ApiModelProperty(value = "总记录数", example = "100")
    private Long total;

    @ApiModelProperty(value = "总页数", example = "10")
    private Long pages;

    @ApiModelProperty(value = "数据列表")
    private List<T> records;

    @ApiModelProperty(value = "是否有上一页")
    private Boolean hasPrevious;

    @ApiModelProperty(value = "是否有下一页")
    private Boolean hasNext;

    @ApiModelProperty(value = "是否为第一页")
    private Boolean isFirst;

    @ApiModelProperty(value = "是否为最后一页")
    private Boolean isLast;

    public PageResult() {
    }

    public PageResult(Long current, Long size, Long total, List<T> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.records = records;
        this.pages = (total + size - 1) / size;
        this.hasPrevious = current > 1;
        this.hasNext = current < pages;
        this.isFirst = current == 1;
        this.isLast = current.equals(pages);
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(Long current, Long size, Long total, List<T> records) {
        return new PageResult<>(current, size, total, records);
    }

    /**
     * 创建空分页结果
     */
    public static <T> PageResult<T> empty(Long current, Long size) {
        return new PageResult<>(current, size, 0L, null);
    }

    /**
     * 从MyBatis Plus的Page对象转换
     */
    public static <T> PageResult<T> fromPage(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        return new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    /**
     * 转换数据类型
     */
    public <R> PageResult<R> convert(List<R> newRecords) {
        PageResult<R> result = new PageResult<>();
        result.setCurrent(this.current);
        result.setSize(this.size);
        result.setTotal(this.total);
        result.setPages(this.pages);
        result.setRecords(newRecords);
        result.setHasPrevious(this.hasPrevious);
        result.setHasNext(this.hasNext);
        result.setIsFirst(this.isFirst);
        result.setIsLast(this.isLast);
        return result;
    }
}
