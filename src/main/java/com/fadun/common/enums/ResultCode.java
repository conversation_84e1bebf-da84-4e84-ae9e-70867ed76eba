package com.fadun.common.enums;

import lombok.Getter;

/**
 * 统一返回状态码枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public enum ResultCode {

    // ========== 通用状态码 ==========
    SUCCESS(200, "操作成功"),
    FAIL(500, "操作失败"),
    
    // ========== 客户端错误 4xx ==========
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    REQUEST_TIMEOUT(408, "请求超时"),
    CONFLICT(409, "资源冲突"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),
    
    // ========== 服务端错误 5xx ==========
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    NOT_IMPLEMENTED(501, "功能未实现"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),
    
    // ========== 业务错误码 1xxx ==========
    PARAM_VALIDATE_ERROR(1001, "参数校验失败"),
    DATA_NOT_FOUND(1002, "数据不存在"),
    DATA_ALREADY_EXISTS(1003, "数据已存在"),
    DATA_OPERATION_ERROR(1004, "数据操作失败"),
    
    // ========== 用户相关错误码 2xxx ==========
    USER_NOT_FOUND(2001, "用户不存在"),
    USER_ALREADY_EXISTS(2002, "用户已存在"),
    USER_PASSWORD_ERROR(2003, "用户密码错误"),
    USER_ACCOUNT_DISABLED(2004, "用户账户已禁用"),
    USER_ACCOUNT_LOCKED(2005, "用户账户已锁定"),
    USER_PERMISSION_DENIED(2006, "用户权限不足"),
    
    // ========== 认证相关错误码 3xxx ==========
    TOKEN_INVALID(3001, "Token无效"),
    TOKEN_EXPIRED(3002, "Token已过期"),
    TOKEN_MISSING(3003, "Token缺失"),
    LOGIN_FAILED(3004, "登录失败"),
    LOGOUT_FAILED(3005, "登出失败"),
    
    // ========== 文件相关错误码 4xxx ==========
    FILE_NOT_FOUND(4001, "文件不存在"),
    FILE_UPLOAD_ERROR(4002, "文件上传失败"),
    FILE_DOWNLOAD_ERROR(4003, "文件下载失败"),
    FILE_TYPE_NOT_SUPPORTED(4004, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(4005, "文件大小超出限制"),
    
    // ========== 数据库相关错误码 5xxx ==========
    DATABASE_ERROR(5001, "数据库操作失败"),
    DATABASE_CONNECTION_ERROR(5002, "数据库连接失败"),
    SQL_SYNTAX_ERROR(5003, "SQL语法错误"),
    DATA_INTEGRITY_VIOLATION(5004, "数据完整性约束违反"),
    
    // ========== 外部服务相关错误码 6xxx ==========
    EXTERNAL_SERVICE_ERROR(6001, "外部服务调用失败"),
    EXTERNAL_SERVICE_TIMEOUT(6002, "外部服务调用超时"),
    EXTERNAL_SERVICE_UNAVAILABLE(6003, "外部服务不可用");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 构造函数
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return FAIL;
    }
}
