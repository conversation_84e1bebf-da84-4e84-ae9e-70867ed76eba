package com.fadun.common.exception;

import com.fadun.common.enums.ResultCode;
import lombok.Getter;

/**
 * 业务异常类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String message;

    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.FAIL.getCode();
        this.message = message;
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public BusinessException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
        this.message = message;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResultCode.FAIL.getCode();
        this.message = message;
    }

    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public BusinessException(ResultCode resultCode, Throwable cause) {
        super(resultCode.getMessage(), cause);
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建业务异常
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }

    /**
     * 创建业务异常
     */
    public static BusinessException of(Integer code, String message) {
        return new BusinessException(code, message);
    }

    /**
     * 创建业务异常
     */
    public static BusinessException of(ResultCode resultCode) {
        return new BusinessException(resultCode);
    }

    /**
     * 创建业务异常
     */
    public static BusinessException of(ResultCode resultCode, String message) {
        return new BusinessException(resultCode, message);
    }

    // ========== 常用业务异常 ==========

    /**
     * 参数错误异常
     */
    public static BusinessException paramError(String message) {
        return new BusinessException(ResultCode.PARAM_VALIDATE_ERROR, message);
    }

    /**
     * 数据不存在异常
     */
    public static BusinessException dataNotFound() {
        return new BusinessException(ResultCode.DATA_NOT_FOUND);
    }

    /**
     * 数据不存在异常
     */
    public static BusinessException dataNotFound(String message) {
        return new BusinessException(ResultCode.DATA_NOT_FOUND, message);
    }

    /**
     * 数据已存在异常
     */
    public static BusinessException dataExists() {
        return new BusinessException(ResultCode.DATA_ALREADY_EXISTS);
    }

    /**
     * 数据已存在异常
     */
    public static BusinessException dataExists(String message) {
        return new BusinessException(ResultCode.DATA_ALREADY_EXISTS, message);
    }

    /**
     * 用户不存在异常
     */
    public static BusinessException userNotFound() {
        return new BusinessException(ResultCode.USER_NOT_FOUND);
    }

    /**
     * 权限不足异常
     */
    public static BusinessException permissionDenied() {
        return new BusinessException(ResultCode.USER_PERMISSION_DENIED);
    }

    /**
     * Token无效异常
     */
    public static BusinessException tokenInvalid() {
        return new BusinessException(ResultCode.TOKEN_INVALID);
    }
}
