package com.fadun.common.utils;

import com.fadun.common.enums.ResultCode;
import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;

import java.util.List;

/**
 * 返回结果工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class ResultUtils {

    // ========== 成功返回 ==========

    /**
     * 成功返回
     */
    public static <T> Result<T> success() {
        return Result.<T>success();
    }

    /**
     * 成功返回带数据
     */
    public static <T> Result<T> success(T data) {
        return Result.success(data);
    }

    /**
     * 成功返回带消息
     */
    public static <T> Result<T> success(String message) {
        return Result.<T>success(message);
    }

    /**
     * 成功返回带消息和数据
     */
    public static <T> Result<T> success(String message, T data) {
        return Result.success(message, data);
    }

    // ========== 失败返回 ==========

    /**
     * 失败返回
     */
    public static <T> Result<T> fail() {
        return Result.<T>fail();
    }

    /**
     * 失败返回带消息
     */
    public static <T> Result<T> fail(String message) {
        return Result.<T>fail(message);
    }

    /**
     * 失败返回带状态码和消息
     */
    public static <T> Result<T> fail(Integer code, String message) {
        return Result.<T>fail(code, message);
    }

    /**
     * 失败返回带枚举
     */
    public static <T> Result<T> fail(ResultCode resultCode) {
        return Result.<T>fail(resultCode);
    }

    /**
     * 失败返回带枚举和数据
     */
    public static <T> Result<T> fail(ResultCode resultCode, T data) {
        return Result.fail(resultCode, data);
    }

    // ========== 分页返回 ==========

    /**
     * 分页成功返回
     */
    public static <T> Result<PageResult<T>> page(PageResult<T> pageResult) {
        return Result.success("查询成功", pageResult);
    }

    /**
     * 分页成功返回带消息
     */
    public static <T> Result<PageResult<T>> page(String message, PageResult<T> pageResult) {
        return Result.success(message, pageResult);
    }

    /**
     * 分页成功返回（直接传参）
     */
    public static <T> Result<PageResult<T>> page(Long current, Long size, Long total, List<T> records) {
        PageResult<T> pageResult = PageResult.of(current, size, total, records);
        return Result.success("查询成功", pageResult);
    }

    /**
     * 空分页返回
     */
    public static <T> Result<PageResult<T>> emptyPage(Long current, Long size) {
        PageResult<T> pageResult = PageResult.empty(current, size);
        return Result.success("查询成功", pageResult);
    }

    // ========== 条件返回 ==========

    /**
     * 根据条件返回成功或失败
     */
    public static <T> Result<T> condition(boolean condition) {
        return condition ? success() : fail();
    }

    /**
     * 根据条件返回成功或失败带消息
     */
    public static <T> Result<T> condition(boolean condition, String successMessage, String failMessage) {
        return condition ? success(successMessage) : fail(failMessage);
    }

    /**
     * 根据条件返回成功或失败带数据
     */
    public static <T> Result<T> condition(boolean condition, T data) {
        return condition ? success(data) : fail();
    }

    /**
     * 根据条件返回成功或失败带消息和数据
     */
    public static <T> Result<T> condition(boolean condition, String successMessage, String failMessage, T data) {
        return condition ? success(successMessage, data) : fail(failMessage);
    }

    // ========== 数据判断返回 ==========

    /**
     * 根据数据是否为空返回结果
     */
    public static <T> Result<T> data(T data) {
        return data != null ? success(data) : fail(ResultCode.DATA_NOT_FOUND);
    }

    /**
     * 根据数据是否为空返回结果带消息
     */
    public static <T> Result<T> data(T data, String successMessage, String failMessage) {
        return data != null ? success(successMessage, data) : fail(failMessage);
    }

}
