package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物资库存实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("material_stock")
@ApiModel(description = "物资库存")
public class MaterialStock implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "物资ID")
    @TableField("material_id")
    private Long materialId;

    @ApiModelProperty(value = "库室ID")
    @TableField("warehouse_id")
    private Long warehouseId;

    @ApiModelProperty(value = "当前库存")
    @TableField("current_stock")
    private Integer currentStock;

    @ApiModelProperty(value = "存放位置")
    @TableField("location")
    private String location;

    @ApiModelProperty(value = "最后入库时间")
    @TableField("last_in_time")
    private LocalDateTime lastInTime;

    @ApiModelProperty(value = "最后出库时间")
    @TableField("last_out_time")
    private LocalDateTime lastOutTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "物资编码")
    @TableField(exist = false)
    private String materialCode;

    @ApiModelProperty(value = "物资名称")
    @TableField(exist = false)
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    @TableField(exist = false)
    private String specification;

    @ApiModelProperty(value = "计量单位")
    @TableField(exist = false)
    private String unit;

    @ApiModelProperty(value = "库室名称")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "总库存（所有库房库存之和）")
    @TableField(exist = false)
    private Integer totalStock;

    @ApiModelProperty(value = "分类ID")
    @TableField(exist = false)
    private Long categoryId;

    @ApiModelProperty(value = "分类名称")
    @TableField(exist = false)
    private String categoryName;
}
