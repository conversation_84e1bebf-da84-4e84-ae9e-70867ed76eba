package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 出库单明细实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("outbound_detail")
@ApiModel(description = "出库单明细")
public class OutboundDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "出库单ID")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "物资ID")
    @TableField("material_id")
    private Long materialId;

    @ApiModelProperty(value = "出库数量")
    @TableField("quantity")
    private Integer quantity;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "物资编码")
    @TableField(exist = false)
    private String materialCode;

    @ApiModelProperty(value = "物资名称")
    @TableField(exist = false)
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    @TableField(exist = false)
    private String specification;

    @ApiModelProperty(value = "计量单位")
    @TableField(exist = false)
    private String unit;
}
