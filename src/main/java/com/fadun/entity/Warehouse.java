package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 库室信息实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("warehouse")
@ApiModel(description = "库室信息")
public class Warehouse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "库室编码")
    @TableField("warehouse_code")
    private String warehouseCode;

    @ApiModelProperty(value = "库室名称")
    @TableField("warehouse_name")
    private String warehouseName;

    @ApiModelProperty(value = "库室位置")
    @TableField("location")
    private String location;

    @ApiModelProperty(value = "管理员ID")
    @TableField("manager_id")
    private Long managerId;

    @ApiModelProperty(value = "管理员姓名")
    @TableField("manager_name")
    private String managerName;

    @ApiModelProperty(value = "联系电话")
    @TableField("manager_phone")
    private String managerPhone;

    @ApiModelProperty(value = "状态：1-正常，0-停用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
}
