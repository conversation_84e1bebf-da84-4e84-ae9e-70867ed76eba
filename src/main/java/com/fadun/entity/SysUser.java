package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统用户实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user")
@ApiModel(description = "系统用户")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户名")
    @TableField("username")
    private String username;

    @ApiModelProperty(value = "密码")
    @TableField("password")
    private String password;

    @ApiModelProperty(value = "真实姓名")
    @TableField("real_name")
    private String realName;

    @ApiModelProperty(value = "手机号")
    @TableField("phone")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    @TableField("email")
    private String email;

    @ApiModelProperty(value = "部门")
    @TableField("department")
    private String department;

    @ApiModelProperty(value = "职位")
    @TableField("position")
    private String position;

    @ApiModelProperty(value = "角色类型：SUPER_ADMIN,LOGISTICS_ADMIN,TRAINING_ADMIN,WORK_ADMIN,LIFE_ADMIN,NORMAL_USER")
    @TableField("role_type")
    private String roleType;

    @ApiModelProperty(value = "状态：1-正常，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // ========== 角色类型常量 ==========
    
    /**
     * 角色类型常量
     */
    public static class RoleType {
        /** 超级管理员 */
        public static final String SUPER_ADMIN = "SUPER_ADMIN";
        /** 战备管理员 */
        public static final String LOGISTICS_ADMIN = "LOGISTICS_ADMIN";
        /** 训练管理员 */
        public static final String TRAINING_ADMIN = "TRAINING_ADMIN";
        /** 工作管理员 */
        public static final String WORK_ADMIN = "WORK_ADMIN";
        /** 生活管理员 */
        public static final String LIFE_ADMIN = "LIFE_ADMIN";
        /** 普通用户 */
        public static final String NORMAL_USER = "NORMAL_USER";
    }

    /**
     * 状态常量
     */
    public static class Status {
        /** 正常 */
        public static final Integer NORMAL = 1;
        /** 禁用 */
        public static final Integer DISABLED = 0;
    }

    // ========== 权限判断方法 ==========

    /**
     * 是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return RoleType.SUPER_ADMIN.equals(this.roleType);
    }

    /**
     * 是否为模块管理员
     */
    public boolean isModuleAdmin() {
        return RoleType.LOGISTICS_ADMIN.equals(this.roleType)
            || RoleType.TRAINING_ADMIN.equals(this.roleType)
            || RoleType.WORK_ADMIN.equals(this.roleType)
            || RoleType.LIFE_ADMIN.equals(this.roleType);
    }

    /**
     * 是否有指定模块的访问权限
     * @param module 模块名称：logistics, training, work, life, system
     */
    public boolean hasModuleAccess(String module) {
        if (isSuperAdmin()) {
            return true;
        }
        
        switch (this.roleType) {
            case RoleType.LOGISTICS_ADMIN:
                return "logistics".equals(module);
            case RoleType.TRAINING_ADMIN:
                return "training".equals(module);
            case RoleType.WORK_ADMIN:
                return "work".equals(module);
            case RoleType.LIFE_ADMIN:
                return "life".equals(module);
            case RoleType.NORMAL_USER:
                return "overview".equals(module);
            default:
                return false;
        }
    }

    /**
     * 获取用户可访问的模块列表
     */
    public String[] getAccessibleModules() {
        if (isSuperAdmin()) {
            return new String[]{"overview", "logistics", "training", "work", "life", "system"};
        }
        
        switch (this.roleType) {
            case RoleType.LOGISTICS_ADMIN:
                return new String[]{"overview", "logistics"};
            case RoleType.TRAINING_ADMIN:
                return new String[]{"overview", "training"};
            case RoleType.WORK_ADMIN:
                return new String[]{"overview", "work"};
            case RoleType.LIFE_ADMIN:
                return new String[]{"overview", "life"};
            case RoleType.NORMAL_USER:
            default:
                return new String[]{"overview"};
        }
    }
}
