package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 入库单实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inbound_order")
@ApiModel(description = "入库单")
public class InboundOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "入库单号")
    @TableField("order_no")
    private String orderNo;

    @ApiModelProperty(value = "库室ID")
    @TableField("warehouse_id")
    private Long warehouseId;

    @ApiModelProperty(value = "供应商")
    @TableField("supplier")
    private String supplier;

    @ApiModelProperty(value = "入库类型：purchase-采购，return-退货，transfer-调拨")
    @TableField("inbound_type")
    private String inboundType;

    @ApiModelProperty(value = "总数量")
    @TableField("total_quantity")
    private Integer totalQuantity;

    @ApiModelProperty(value = "操作员ID")
    @TableField("operator_id")
    private Long operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @TableField("operator_name")
    private String operatorName;

    @ApiModelProperty(value = "入库时间")
    @TableField("inbound_time")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "状态：1-待入库，2-已入库，3-已取消")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "库室名称")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "入库明细列表")
    @TableField(exist = false)
    private List<InboundDetail> details;

    @ApiModelProperty(value = "状态名称")
    @TableField(exist = false)
    private String statusName;
}
