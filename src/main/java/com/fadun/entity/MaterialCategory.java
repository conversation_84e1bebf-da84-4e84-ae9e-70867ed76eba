package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 物资分类实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("material_category")
@ApiModel(description = "物资分类")
public class MaterialCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "分类编码")
    @TableField("category_code")
    private String categoryCode;

    @ApiModelProperty(value = "分类名称")
    @TableField("category_name")
    private String categoryName;

    @ApiModelProperty(value = "父级分类ID")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "分类层级")
    @TableField("level")
    private Integer level;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "父级分类名称")
    @TableField(exist = false)
    private String parentName;

    @ApiModelProperty(value = "子分类列表")
    @TableField(exist = false)
    private List<MaterialCategory> children;

    @ApiModelProperty(value = "物资数量")
    @TableField(exist = false)
    private Integer materialCount;
}
