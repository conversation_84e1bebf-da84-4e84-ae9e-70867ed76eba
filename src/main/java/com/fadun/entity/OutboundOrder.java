package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 出库单实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("outbound_order")
@ApiModel(description = "出库单")
public class OutboundOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "出库单号")
    @TableField("order_no")
    private String orderNo;

    @ApiModelProperty(value = "库室ID")
    @TableField("warehouse_id")
    private Long warehouseId;

    @ApiModelProperty(value = "出库类型：use-使用，transfer-调拨，scrap-报废")
    @TableField("outbound_type")
    private String outboundType;

    @ApiModelProperty(value = "接收单位")
    @TableField("receiver_unit")
    private String receiverUnit;

    @ApiModelProperty(value = "接收人")
    @TableField("receiver_name")
    private String receiverName;

    @ApiModelProperty(value = "接收人电话")
    @TableField("receiver_phone")
    private String receiverPhone;

    @ApiModelProperty(value = "总数量")
    @TableField("total_quantity")
    private Integer totalQuantity;

    @ApiModelProperty(value = "操作员ID")
    @TableField("operator_id")
    private Long operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @TableField("operator_name")
    private String operatorName;

    @ApiModelProperty(value = "出库时间")
    @TableField("outbound_time")
    private LocalDateTime outboundTime;

    @ApiModelProperty(value = "状态：1-待出库，2-已出库，3-已取消")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "库室名称")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "出库明细列表")
    @TableField(exist = false)
    private List<OutboundDetail> details;

    @ApiModelProperty(value = "状态名称")
    @TableField(exist = false)
    private String statusName;
}
