package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 保养记录实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("maintenance_record")
@ApiModel(description = "保养记录")
public class MaintenanceRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "物资ID")
    @TableField("material_id")
    private Long materialId;

    @ApiModelProperty(value = "库室ID")
    @TableField("warehouse_id")
    private Long warehouseId;

    @ApiModelProperty(value = "保养类型：routine-例行，special-专项，emergency-应急")
    @TableField("maintenance_type")
    private String maintenanceType;

    @ApiModelProperty(value = "保养日期")
    @TableField("maintenance_date")
    private LocalDate maintenanceDate;

    @ApiModelProperty(value = "保养内容")
    @TableField("maintenance_content")
    private String maintenanceContent;

    @ApiModelProperty(value = "保养结果：normal-正常，abnormal-异常，repair-需维修")
    @TableField("maintenance_result")
    private String maintenanceResult;

    @ApiModelProperty(value = "保养员ID")
    @TableField("operator_id")
    private Long operatorId;

    @ApiModelProperty(value = "保养员姓名")
    @TableField("operator_name")
    private String operatorName;

    @ApiModelProperty(value = "下次保养日期")
    @TableField("next_maintenance_date")
    private LocalDate nextMaintenanceDate;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "物资编码")
    @TableField(exist = false)
    private String materialCode;

    @ApiModelProperty(value = "物资名称")
    @TableField(exist = false)
    private String materialName;

    @ApiModelProperty(value = "库室名称")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "保养类型名称")
    @TableField(exist = false)
    private String maintenanceTypeName;

    @ApiModelProperty(value = "保养结果名称")
    @TableField(exist = false)
    private String maintenanceResultName;
}
