# 法盾项目 API 接口文档

## 项目概述

**项目名称**: 法盾项目 - 战备模块物资管理系统  
**版本**: v1.0.0  
**技术栈**: Spring Boot 2.7.15 + MyBatis Plus + MySQL  
**基础路径**: http://localhost:8080/fadun  
**API文档地址**: http://localhost:8080/fadun/doc.html  

## 文档导航

本API文档按功能模块进行了拆分，便于查阅和维护：

### 📋 基础规范
- [**API规范与数据模型**](./API规范与数据模型.md) - 接口规范、返回格式、状态码、数据模型定义

### 🎯 功能模块

| 模块 | 文档链接 | 说明 |
|------|----------|------|
| **总览模块** | [总览模块API.md](./总览模块API.md) | 战备模块数据统计和功能导航 |
| **物资管理** | [物资管理API.md](./物资管理API.md) | 物资台账和分类管理 |
| **库室管理** | [库室管理API.md](./库室管理API.md) | 库室信息和统计管理 |
| **出入库管理** | [出入库管理API.md](./出入库管理API.md) | 物资入库、出库和明细管理 |
| **库存管理** | [库存管理API.md](./库存管理API.md) | 库存查询、统计和盘点管理 |
| **保养记录** | [保养记录API.md](./保养记录API.md) | 物资保养记录和提醒管理 |
| **权限管理** | [权限管理API.md](./权限管理API.md) | 用户、角色、权限管理和分配 |
| **系统管理** | [系统管理API.md](./系统管理API.md) | 数据库更新等系统功能 |

## 快速开始

### 1. 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 运行项目

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

### 3. 访问应用

- 应用地址: http://localhost:8080/fadun
- **API文档**: http://localhost:8080/fadun/doc.html

## 接口特色

### 🎯 统一返回体封装
- **Result<T>**: 统一API返回格式
- **PageResult<T>**: 分页数据返回格式
- **ResultCode**: 统一状态码枚举
- **GlobalExceptionHandler**: 全局异常处理

### 📦 战备模块特点
- **纯数量管理**: 专注于物资数量，不涉及金额计算
- **简化设计**: 去除保质期、生产日期等复杂字段
- **简洁表名**: 数据库表名简洁明了，无冗余前缀
- **实用导向**: 适合基础物资管理场景

### 📚 完整API文档
- 集成Knife4j，提供美观的API文档界面
- 支持在线测试和调试
- 完整的接口注解和示例

## 核心功能

### 物资管理
- 📋 **物资台账**: 完整的CRUD操作、分类筛选
- 🗂️ **物资分类**: 树形结构管理、拖拽排序

### 库存管理
- 📦 **库存查询**: 库存查询、低库存预警、库存盘点
- 🏪 **库室管理**: 库室信息管理、统计分析

### 出入库管理
- ⬆️ **物资入库**: 入库单管理、状态流转
- ⬇️ **物资出库**: 出库单管理、接收人信息
- 📝 **明细管理**: 批量操作入库出库明细

### 保养记录
- 🔧 **保养记录**: 保养计划、维修记录
- ⏰ **提醒功能**: 保养提醒、历史记录查询

### 数据总览
- 📊 **数据统计**: 各模块数据统计、趋势分析
- 🎛️ **功能导航**: 模块导航、快捷操作

## 更新日志

- **v1.0.0** (2024-01-20): 初始版本，包含物资管理核心功能
  - 完成物资台账、分类、库室管理
  - 实现出入库操作和库存管理
  - 添加保养记录功能
  - 提供数据统计和总览功能

## 开发团队

- **开发团队**: 法盾团队
- **技术架构**: Spring Boot + MyBatis Plus
- **文档维护**: 按模块分离，便于维护
