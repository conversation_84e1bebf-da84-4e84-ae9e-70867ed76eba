# API规范与数据模型

## 接口规范

### 统一返回格式

#### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

#### 失败响应
```json
{
  "code": 500,
  "message": "操作失败",
  "data": null
}
```

#### 分页响应
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 100,
    "pages": 10,
    "records": []
  }
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 参数校验失败 |
| 1002 | 数据不存在 |
| 1003 | 数据已存在 |

### 请求规范

#### HTTP方法
- `GET`: 查询操作
- `POST`: 创建操作  
- `PUT`: 更新操作
- `DELETE`: 删除操作

#### 内容类型
- 请求头: `Content-Type: application/json`
- 响应头: `Content-Type: application/json;charset=UTF-8`

#### 分页参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |

---

## 数据模型

### Material - 物资信息
```json
{
  "id": "Long - 主键ID",
  "materialCode": "String - 物资编码",
  "materialName": "String - 物资名称",
  "categoryId": "Long - 分类ID",
  "specification": "String - 规格型号",
  "unit": "String - 计量单位",
  "brand": "String - 品牌",
  "model": "String - 型号",
  "status": "Integer - 状态（1-正常，0-停用）",
  "remark": "String - 备注",
  "createTime": "LocalDateTime - 创建时间",
  "updateTime": "LocalDateTime - 更新时间",
  "createBy": "String - 创建人",
  "updateBy": "String - 更新人",
  "categoryName": "String - 分类名称（扩展字段）"
}
```

### MaterialCategory - 物资分类
```json
{
  "id": "Long - 主键ID",
  "categoryCode": "String - 分类编码",
  "categoryName": "String - 分类名称",
  "parentId": "Long - 父级分类ID",
  "level": "Integer - 分类层级",
  "remark": "String - 备注",
  "parentName": "String - 父级分类名称（扩展字段）",
  "children": "List<MaterialCategory> - 子分类列表（扩展字段）",
  "materialCount": "Integer - 物资数量（扩展字段）"
}
```

### Warehouse - 库室信息
```json
{
  "id": "Long - 主键ID",
  "warehouseCode": "String - 库室编码",
  "warehouseName": "String - 库室名称",
  "location": "String - 库室位置",
  "managerId": "Long - 管理员ID",
  "managerName": "String - 管理员姓名",
  "managerPhone": "String - 联系电话",
  "status": "Integer - 状态（1-正常，0-停用）",
  "remark": "String - 备注"
}
```

### InboundOrder - 入库单
```json
{
  "id": "Long - 主键ID",
  "orderNo": "String - 入库单号",
  "warehouseId": "Long - 库室ID",
  "supplier": "String - 供应商",
  "inboundType": "String - 入库类型（purchase-采购，return-退货，transfer-调拨）",
  "totalQuantity": "Integer - 总数量",
  "operatorId": "Long - 操作员ID",
  "operatorName": "String - 操作员姓名",
  "inboundTime": "LocalDateTime - 入库时间",
  "status": "Integer - 状态（1-待入库，2-已入库，3-已取消）",
  "remark": "String - 备注",
  "createTime": "LocalDateTime - 创建时间",
  "updateTime": "LocalDateTime - 更新时间",
  "createBy": "String - 创建人",
  "updateBy": "String - 更新人",
  "warehouseName": "String - 库室名称（扩展字段）",
  "details": "List<InboundDetail> - 入库明细列表（扩展字段）",
  "statusName": "String - 状态名称（扩展字段）"
}
```

### OutboundOrder - 出库单
```json
{
  "id": "Long - 主键ID",
  "orderNo": "String - 出库单号",
  "warehouseId": "Long - 库室ID",
  "outboundType": "String - 出库类型（use-使用，transfer-调拨，scrap-报废）",
  "receiverUnit": "String - 接收单位",
  "receiverName": "String - 接收人",
  "receiverPhone": "String - 接收人电话",
  "totalQuantity": "Integer - 总数量",
  "operatorId": "Long - 操作员ID",
  "operatorName": "String - 操作员姓名",
  "outboundTime": "LocalDateTime - 出库时间",
  "status": "Integer - 状态（1-待出库，2-已出库，3-已取消）",
  "remark": "String - 备注",
  "createTime": "LocalDateTime - 创建时间",
  "updateTime": "LocalDateTime - 更新时间",
  "createBy": "String - 创建人",
  "updateBy": "String - 更新人",
  "warehouseName": "String - 库室名称（扩展字段）",
  "details": "List<OutboundDetail> - 出库明细列表（扩展字段）",
  "statusName": "String - 状态名称（扩展字段）"
}
```

### InboundDetail - 入库单明细
```json
{
  "id": "Long - 主键ID",
  "orderId": "Long - 入库单ID",
  "materialId": "Long - 物资ID",
  "quantity": "Integer - 入库数量",
  "remark": "String - 备注",
  "materialCode": "String - 物资编码（扩展字段）",
  "materialName": "String - 物资名称（扩展字段）",
  "specification": "String - 规格型号（扩展字段）",
  "unit": "String - 计量单位（扩展字段）"
}
```

### OutboundDetail - 出库单明细
```json
{
  "id": "Long - 主键ID",
  "orderId": "Long - 出库单ID",
  "materialId": "Long - 物资ID",
  "quantity": "Integer - 出库数量",
  "remark": "String - 备注",
  "materialCode": "String - 物资编码（扩展字段）",
  "materialName": "String - 物资名称（扩展字段）",
  "specification": "String - 规格型号（扩展字段）",
  "unit": "String - 计量单位（扩展字段）"
}
```

### MaterialStock - 物资库存
```json
{
  "id": "Long - 主键ID",
  "materialId": "Long - 物资ID",
  "warehouseId": "Long - 库室ID",
  "currentStock": "Integer - 当前库存",
  "location": "String - 存放位置",
  "lastInTime": "LocalDateTime - 最后入库时间",
  "lastOutTime": "LocalDateTime - 最后出库时间",
  "updateTime": "LocalDateTime - 更新时间",
  "materialCode": "String - 物资编码（扩展字段）",
  "materialName": "String - 物资名称（扩展字段）",
  "specification": "String - 规格型号（扩展字段）",
  "unit": "String - 计量单位（扩展字段）",
  "warehouseName": "String - 库室名称（扩展字段）",
  "totalStock": "Integer - 总库存（扩展字段）",
  "categoryId": "Long - 分类ID（扩展字段）",
  "categoryName": "String - 分类名称（扩展字段）"
}
```

### MaintenanceRecord - 保养记录
```json
{
  "id": "Long - 主键ID",
  "materialId": "Long - 物资ID",
  "warehouseId": "Long - 库室ID",
  "maintenanceType": "String - 保养类型（routine-例行，special-专项，emergency-应急）",
  "maintenanceDate": "LocalDate - 保养日期",
  "maintenanceContent": "String - 保养内容",
  "maintenanceResult": "String - 保养结果（normal-正常，abnormal-异常，repair-需维修）",
  "operatorId": "Long - 保养员ID",
  "operatorName": "String - 保养员姓名",
  "nextMaintenanceDate": "LocalDate - 下次保养日期",
  "remark": "String - 备注",
  "createTime": "LocalDateTime - 创建时间",
  "updateTime": "LocalDateTime - 更新时间",
  "createBy": "String - 创建人",
  "updateBy": "String - 更新人",
  "materialCode": "String - 物资编码（扩展字段）",
  "materialName": "String - 物资名称（扩展字段）",
  "warehouseName": "String - 库室名称（扩展字段）",
  "maintenanceTypeName": "String - 保养类型名称（扩展字段）",
  "maintenanceResultName": "String - 保养结果名称（扩展字段）"
}
```

---

## 枚举值说明

### 入库类型 (inboundType)
- `purchase`: 采购入库
- `return`: 退货入库
- `transfer`: 调拨入库

### 出库类型 (outboundType)
- `use`: 使用出库
- `transfer`: 调拨出库
- `scrap`: 报废出库

### 保养类型 (maintenanceType)
- `routine`: 例行保养
- `special`: 专项保养
- `emergency`: 应急保养

### 保养结果 (maintenanceResult)
- `normal`: 正常
- `abnormal`: 异常
- `repair`: 需维修

### 状态码 (status)
- **物资/库室**: `1-正常，0-停用`
- **入库单/出库单**: `1-待处理，2-已完成，3-已取消`

---

## 注意事项

1. **时间格式**: 
   - **完整时间**：采用 `yyyy-MM-dd HH:mm:ss` 格式，如：`2024-01-20 14:30:00`
   - **日期格式**：采用 `yyyy-MM-dd` 格式，如：`2024-01-20`
   - **时区说明**：所有时间均为北京时间（GMT+8）
2. **分页查询**: 默认页码从1开始，默认每页10条记录
3. **状态管理**: 大部分实体都有状态字段，删除操作通常是逻辑删除（更新状态）
4. **权限控制**: 当前文档未包含权限相关接口，实际部署时需要添加认证授权机制
5. **数据校验**: 所有创建和更新接口都有数据校验，详细校验规则请参考实体类注解
6. **事务处理**: 涉及库存变更的操作（入库/出库确认）都使用事务保证数据一致性
7. **扩展字段**: 标记为"扩展字段"的属性在数据库中不存在，仅用于前端显示
8. **必填校验**: 创建和更新接口中的必填字段请参考具体接口文档说明
