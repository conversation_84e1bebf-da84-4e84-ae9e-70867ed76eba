# 物资管理 API

## 模块概述

物资管理模块包含物资台账管理和物资分类管理两个核心功能，提供完整的物资信息维护和分类体系管理。

**包含子模块**:
- 物资台账管理 (`/api/material`)
- 物资分类管理 (`/api/category`)

---

## 物资台账管理

### 1. 分页查询物资台账

**接口路径**: `GET /api/material/page`  
**接口描述**: 获取物资台账信息列表，支持关键词搜索和分类筛选  

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| keyword | String | 否 | - | 搜索关键词（物资名称/编码） |
| categoryId | Long | 否 | - | 物资分类ID |
| warehouseId | Long | 否 | - | 库室ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询物资台账成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 156,
    "records": [
      {
        "id": 1,
        "materialCode": "WZ001",
        "materialName": "手电筒",
        "categoryId": 1,
        "categoryName": "照明设备",
        "specification": "LED强光手电筒",
        "unit": "个",
        "brand": "雅格",
        "model": "YG-3238",
        "status": 1,
        "remark": "防水防摔",
        "createTime": "2024-01-01T00:00:00",
        "updateTime": "2024-01-20T14:20:00"
      }
    ]
  }
}
```

### 2. 获取物资详情

**接口路径**: `GET /api/material/{id}`  
**接口描述**: 根据物资ID获取详细信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 物资ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取物资详情成功",
  "data": {
    "id": 1,
    "materialCode": "WZ001",
    "materialName": "手电筒",
    "categoryId": 1,
    "categoryName": "照明设备",
    "specification": "LED强光手电筒",
    "unit": "个",
    "brand": "雅格",
    "model": "YG-3238",
    "status": 1,
    "remark": "防水防摔",
    "createTime": "2024-01-01T00:00:00",
    "updateTime": "2024-01-20T14:20:00",
    "createBy": "system",
    "updateBy": "admin"
  }
}
```

### 3. 创建物资信息

**接口路径**: `POST /api/material/create`  
**接口描述**: 新增物资台账信息  

**请求体**:
```json
{
  "materialCode": "WZ002",
  "materialName": "头盔",
  "categoryId": 2,
  "specification": "ABS安全头盔",
  "unit": "顶",
  "brand": "3M",
  "model": "H-701R",
  "status": 1,
  "remark": "红色"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建物资成功",
  "data": null
}
```

### 4. 更新物资信息

**接口路径**: `PUT /api/material/{id}`  
**接口描述**: 修改物资台账信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 物资ID |

**请求体**: 同创建接口

### 5. 删除物资信息

**接口路径**: `DELETE /api/material/{id}`  
**接口描述**: 删除物资台账信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 物资ID |

---

## 物资分类管理

### 1. 获取分类树形结构

**接口路径**: `GET /api/category/tree`  
**接口描述**: 获取完整的分类树形结构，用于前端树形组件  

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取分类树成功",
  "data": [
    {
      "id": 1,
      "categoryCode": "001",
      "categoryName": "防护用品",
      "parentId": 0,
      "level": 1,
      "remark": "个人防护装备",
      "children": [
        {
          "id": 2,
          "categoryCode": "001001",
          "categoryName": "头部防护",
          "parentId": 1,
          "level": 2,
          "remark": "头盔等",
          "children": []
        },
        {
          "id": 3,
          "categoryCode": "001002",
          "categoryName": "手部防护",
          "parentId": 1,
          "level": 2,
          "remark": "手套等",
          "children": []
        }
      ]
    },
    {
      "id": 4,
      "categoryCode": "002",
      "categoryName": "照明设备",
      "parentId": 0,
      "level": 1,
      "remark": "各类照明设备",
      "children": []
    }
  ]
}
```

### 2. 获取分类列表

**接口路径**: `GET /api/category/list`  
**接口描述**: 获取分类列表，按层级和排序排列  

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取分类列表成功",
  "data": [
    {
      "id": 1,
      "categoryCode": "001",
      "categoryName": "防护用品",
      "parentId": 0,
      "level": 1,
      "remark": "个人防护装备"
    },
    {
      "id": 2,
      "categoryCode": "001001",
      "categoryName": "头部防护",
      "parentId": 1,
      "level": 2,
      "remark": "头盔等"
    }
  ]
}
```

### 3. 获取子分类列表

**接口路径**: `GET /api/category/children/{parentId}`  
**接口描述**: 根据父分类ID获取子分类列表  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parentId | Long | 是 | 父分类ID |

### 4. 获取分类详情

**接口路径**: `GET /api/category/{id}`  
**接口描述**: 根据分类ID获取详细信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 分类ID |

### 5. 创建分类

**接口路径**: `POST /api/category/create`  
**接口描述**: 新增物资分类  

**请求体**:
```json
{
  "categoryCode": "003",
  "categoryName": "通讯设备",
  "parentId": 0,
  "level": 1,
  "remark": "各类通讯设备"
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| categoryCode | String | 是 | 分类编码，需唯一 |
| categoryName | String | 是 | 分类名称 |
| parentId | Long | 是 | 父分类ID，顶级分类为0 |
| level | Integer | 是 | 分类层级，从1开始 |
| remark | String | 否 | 备注说明 |

### 6. 更新分类

**接口路径**: `PUT /api/category/{id}`  
**接口描述**: 修改分类信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 分类ID |

**请求体**: 同创建接口

### 7. 删除分类

**接口路径**: `DELETE /api/category/{id}`  
**接口描述**: 删除分类（需检查是否有子分类或物资）  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 分类ID |

**注意事项**:
- 有子分类的分类不能删除
- 有关联物资的分类不能删除
- 删除前会进行依赖检查

### 8. 检查分类编码

**接口路径**: `GET /api/category/check-code`  
**接口描述**: 检查分类编码是否已存在  

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryCode | String | 是 | 分类编码 |
| excludeId | Long | 否 | 排除的分类ID（用于更新时检查） |

**响应示例**:
```json
{
  "code": 200,
  "message": "检查完成",
  "data": true
}
```

**返回值说明**:
- `true`: 编码可用
- `false`: 编码已存在

---

## 业务规则

### 物资台账
1. **物资编码唯一性**: 每个物资的编码必须唯一
2. **分类关联**: 物资必须关联到有效的分类
3. **状态管理**: 停用状态的物资不能进行出入库操作
4. **删除限制**: 有库存记录的物资不能直接删除

### 物资分类
1. **编码唯一性**: 分类编码在系统内必须唯一
2. **层级结构**: 支持多级分类，建议不超过3级
3. **删除限制**: 
   - 有子分类的分类不能删除
   - 有关联物资的分类不能删除
4. **层级计算**: 层级从1开始，子分类的层级=父分类层级+1

---

## 使用建议

### 分类设计
- 建议建立清晰的分类体系，避免过深的层级
- 分类编码建议使用有意义的规则，如：001、001001等
- 定期整理分类结构，删除无用分类

### 物资管理
- 物资编码建议使用统一规范，便于查找和管理
- 及时更新物资信息，保持数据准确性
- 合理使用状态管理，避免误操作
