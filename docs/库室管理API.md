# 库室管理 API

## 模块概述

库室管理模块提供库室信息的完整管理功能，包括库室的增删改查、统计分析等功能。

**模块路径**: `/api/warehouse`

---

## 接口列表

### 1. 分页查询库室信息

**接口路径**: `GET /api/warehouse/page`  
**接口描述**: 支持关键词搜索和状态筛选  

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| keyword | String | 否 | - | 搜索关键词（库室名称/编码/位置） |
| status | Integer | 否 | - | 状态（1-正常，0-停用） |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 5,
    "records": [
      {
        "id": 1,
        "warehouseCode": "KF001",
        "warehouseName": "主库房",
        "location": "1号楼1层",
        "managerId": 1,
        "managerName": "张三",
        "managerPhone": "13800138000",
        "status": 1,
        "remark": "主要存储库房"
      },
      {
        "id": 2,
        "warehouseCode": "KF002",
        "warehouseName": "辅助库房",
        "location": "1号楼2层",
        "managerId": 2,
        "managerName": "李四",
        "managerPhone": "13800138001",
        "status": 1,
        "remark": "辅助存储库房"
      }
    ]
  }
}
```

### 2. 获取库室列表

**接口路径**: `GET /api/warehouse/list`  
**接口描述**: 获取所有启用状态的库室列表  

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取库室列表成功",
  "data": [
    {
      "id": 1,
      "warehouseCode": "KF001",
      "warehouseName": "主库房",
      "location": "1号楼1层",
      "managerId": 1,
      "managerName": "张三",
      "managerPhone": "13800138000",
      "status": 1,
      "remark": "主要存储库房"
    },
    {
      "id": 2,
      "warehouseCode": "KF002",
      "warehouseName": "辅助库房",
      "location": "1号楼2层",
      "managerId": 2,
      "managerName": "李四",
      "managerPhone": "13800138001",
      "status": 1,
      "remark": "辅助存储库房"
    }
  ]
}
```

### 3. 获取库室详情

**接口路径**: `GET /api/warehouse/{id}`  
**接口描述**: 根据库室ID获取详细信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 库室ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取库室详情成功",
  "data": {
    "id": 1,
    "warehouseCode": "KF001",
    "warehouseName": "主库房",
    "location": "1号楼1层",
    "managerId": 1,
    "managerName": "张三",
    "managerPhone": "13800138000",
    "status": 1,
    "remark": "主要存储库房"
  }
}
```

### 4. 创建库室

**接口路径**: `POST /api/warehouse/create`  
**接口描述**: 新增库室信息  

**请求体**:
```json
{
  "warehouseCode": "KF003",
  "warehouseName": "临时库房",
  "location": "2号楼1层",
  "managerId": 3,
  "managerName": "王五",
  "managerPhone": "13800138002",
  "status": 1,
  "remark": "临时存储使用"
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| warehouseCode | String | 是 | 库室编码，需唯一 |
| warehouseName | String | 是 | 库室名称 |
| location | String | 是 | 库室位置 |
| managerId | Long | 否 | 管理员ID |
| managerName | String | 否 | 管理员姓名 |
| managerPhone | String | 否 | 联系电话 |
| status | Integer | 否 | 状态，默认1（正常） |
| remark | String | 否 | 备注说明 |

**响应示例**:
```json
{
  "code": 200,
  "message": "创建库室成功",
  "data": null
}
```

### 5. 更新库室

**接口路径**: `PUT /api/warehouse/{id}`  
**接口描述**: 修改库室信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 库室ID |

**请求体**: 同创建接口

**响应示例**:
```json
{
  "code": 200,
  "message": "更新库室成功",
  "data": null
}
```

### 6. 删除库室

**接口路径**: `DELETE /api/warehouse/{id}`  
**接口描述**: 删除库室（需检查是否有关联物资）  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 库室ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "删除库室成功",
  "data": null
}
```

**注意事项**:
- 有库存记录的库室不能删除
- 有关联入库/出库单的库室不能删除
- 删除前会进行依赖检查

### 7. 检查库室编码

**接口路径**: `GET /api/warehouse/check-code`  
**接口描述**: 检查库室编码是否已存在  

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| warehouseCode | String | 是 | 库室编码 |
| excludeId | Long | 否 | 排除的库室ID（用于更新时检查） |

**响应示例**:
```json
{
  "code": 200,
  "message": "检查完成",
  "data": true
}
```

**返回值说明**:
- `true`: 编码可用
- `false`: 编码已存在

### 8. 获取库室统计

**接口路径**: `GET /api/warehouse/statistics`  
**接口描述**: 获取库室及其物资数量统计  

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取库室统计成功",
  "data": [
    {
      "id": 1,
      "warehouseCode": "KF001",
      "warehouseName": "主库房",
      "location": "1号楼1层",
      "managerId": 1,
      "managerName": "张三",
      "managerPhone": "13800138000",
      "status": 1,
      "remark": "主要存储库房",
      "materialCount": 45,
      "totalStock": 1580
    },
    {
      "id": 2,
      "warehouseCode": "KF002",
      "warehouseName": "辅助库房",
      "location": "1号楼2层",
      "managerId": 2,
      "managerName": "李四",
      "managerPhone": "13800138001",
      "status": 1,
      "remark": "辅助存储库房",
      "materialCount": 28,
      "totalStock": 890
    }
  ]
}
```

**扩展字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| materialCount | Integer | 库室内物资种类数量 |
| totalStock | Integer | 库室内总库存数量 |

---

## 业务规则

### 库室管理
1. **编码唯一性**: 库室编码在系统内必须唯一
2. **状态管理**: 停用状态的库室不能进行新的出入库操作
3. **删除限制**: 
   - 有库存记录的库室不能删除
   - 有关联出入库单的库室不能删除
4. **管理员信息**: 管理员信息为可选，但建议填写完整

### 数据校验
1. **必填字段**: 库室编码、名称、位置为必填
2. **长度限制**: 
   - 库室编码: 最大20字符
   - 库室名称: 最大50字符
   - 位置信息: 最大100字符
   - 电话号码: 符合手机号格式
3. **状态值**: 只能是0（停用）或1（正常）

---

## 使用建议

### 库室编码规范
- 建议使用有意义的编码规则，如：KF001、KF002等
- 可按楼层、区域等进行编码分类
- 保持编码的连续性和可扩展性

### 库室管理
- 及时更新管理员信息，确保联系方式有效
- 定期检查库室状态，停用不再使用的库室
- 合理规划库室用途，避免资源浪费

### 统计数据用途
- 可用于库室利用率分析
- 支持库存分布统计
- 便于库室容量规划
