# 系统管理 API

## 模块概述

系统管理模块提供系统级的管理功能，包括数据库更新、系统维护等临时性管理接口。

**模块路径**: `/api/database`

---

## 数据库管理

### 1. 更新数据库

**接口路径**: `POST /api/database/update`  
**接口描述**: 更新表结构并插入库存数据（临时接口）  

**请求参数**: 无

**功能说明**:
此接口主要用于系统初始化或数据库结构调整，执行以下操作：

1. **删除冗余字段**:
   - 删除 `material_stock` 表的 `available_stock` 字段
   - 删除 `material_stock` 表的 `frozen_stock` 字段
   - 删除 `inbound_detail` 表的 `batch_no` 和 `location` 字段
   - 删除 `outbound_detail` 表的 `batch_no` 和 `location` 字段

2. **初始化库存数据**:
   - 清空 `material_stock` 表
   - 插入初始库存数据（10条记录）

**响应示例**:
```json
{
  "code": 200,
  "message": "数据库更新成功",
  "data": null
}
```

**失败响应示例**:
```json
{
  "code": 500,
  "message": "数据库更新失败: 具体错误信息",
  "data": null
}
```

**插入的初始数据**:
```sql
INSERT INTO material_stock (material_id, warehouse_id, current_stock, location, last_in_time, last_out_time, update_time) VALUES 
(1, 1, 40, 'A区-01架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'),
(2, 1, 310, 'A区-02架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'),
(3, 2, 8, 'B区-01架', '2024-01-05 11:00:00', '2024-01-12 09:30:00', '2024-01-12 09:30:00'),
(4, 1, 3, 'C区-01架', NULL, NULL, '2024-01-01 00:00:00'),
(5, 3, 90, 'D区-01架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'),
(6, 3, 95, 'D区-02架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'),
(7, 2, 12, 'B区-02架', NULL, NULL, '2024-01-01 00:00:00'),
(8, 3, 25, 'D区-03架', NULL, NULL, '2024-01-01 00:00:00'),
(9, 1, 120, 'A区-03架', NULL, NULL, '2024-01-01 00:00:00'),
(10, 4, 15, 'E区-01架', '2024-01-20 14:00:00', NULL, '2024-01-20 14:00:00');
```

---

## 使用说明

### 使用场景
1. **系统初始化**: 新部署系统时初始化基础数据
2. **数据库升级**: 系统升级时调整数据库结构
3. **数据重置**: 开发测试环境数据重置
4. **结构优化**: 清理历史冗余字段

### 注意事项
1. **生产环境慎用**: 此接口会清空库存数据，生产环境需谨慎使用
2. **权限控制**: 建议只有系统管理员才能调用此接口
3. **数据备份**: 执行前建议备份重要数据
4. **依赖检查**: 确认相关表结构和约束正确

### 安全建议
1. **访问控制**: 
   - 限制接口访问IP
   - 要求管理员身份验证
   - 记录操作日志

2. **操作确认**:
   - 添加二次确认机制
   - 提供操作预览功能
   - 支持操作回滚

3. **监控告警**:
   - 监控接口调用情况
   - 异常操作及时告警
   - 定期审计操作记录

---

## 错误处理

### 常见错误
1. **表不存在**: 数据库表结构不完整
2. **字段不存在**: 要删除的字段已经不存在（会忽略此错误）
3. **权限不足**: 数据库用户权限不够
4. **数据约束**: 违反外键约束或其他约束

### 错误响应格式
```json
{
  "code": 500,
  "message": "数据库更新失败: 具体错误描述",
  "data": null,
  "timestamp": "2024-01-20T14:30:00"
}
```

### 处理建议
1. **检查日志**: 查看详细错误日志
2. **验证权限**: 确认数据库用户权限
3. **检查约束**: 验证数据表结构和约束
4. **分步执行**: 必要时可以分步执行各个操作

---

## 开发指南

### 扩展接口
如需添加其他系统管理功能，建议：

1. **新增接口**:
   ```java
   @PostMapping("/backup")
   public Result<String> backupDatabase()
   
   @PostMapping("/restore")
   public Result<String> restoreDatabase(@RequestParam String backupFile)
   ```

2. **统一规范**:
   - 使用POST方法执行危险操作
   - 统一返回格式
   - 详细记录操作日志

3. **安全措施**:
   - 添加权限验证
   - 实现操作确认
   - 提供撤销功能

### 最佳实践
1. **事务控制**: 确保操作的原子性
2. **异常处理**: 全面的异常捕获和处理
3. **日志记录**: 详细记录操作过程
4. **参数校验**: 严格的输入参数验证

---

## 临时性说明

⚠️ **重要提示**: 本模块中的接口主要为临时性管理接口，主要用于：

- 系统开发阶段的数据初始化
- 测试环境的数据重置
- 数据库结构的调整优化

正式生产环境中，建议：
1. 移除或禁用临时接口
2. 使用专业的数据库管理工具
3. 建立完善的数据备份和恢复机制
4. 实施严格的变更管理流程
