# 保养记录 API

## 模块概述

保养记录模块提供物资保养记录的完整管理功能，包括保养记录的增删改查、历史记录查询和保养提醒功能。

**模块路径**: `/api/maintenance`

---

## 接口列表

### 1. 分页查询保养记录

**接口路径**: `GET /api/maintenance/page`  
**接口描述**: 获取物资保养记录列表  

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| keyword | String | 否 | - | 搜索关键词（物资名称/编码） |
| materialId | Long | 否 | - | 物资ID |
| maintenanceType | String | 否 | - | 保养类型 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询保养记录成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "records": [
      {
        "id": 1,
        "materialId": 1,
        "materialCode": "WZ001",
        "materialName": "手电筒",
        "warehouseId": 1,
        "warehouseName": "主库房",
        "maintenanceType": "routine",
        "maintenanceTypeName": "例行保养",
        "maintenanceDate": "2024-01-15",
        "maintenanceContent": "清洁外壳，检查电池",
        "maintenanceResult": "normal",
        "maintenanceResultName": "正常",
        "operatorId": 1,
        "operatorName": "王五",
        "nextMaintenanceDate": "2024-04-15",
        "remark": "设备正常",
        "createTime": "2024-01-15T10:00:00"
      },
      {
        "id": 2,
        "materialId": 3,
        "materialCode": "WZ003",
        "materialName": "对讲机",
        "warehouseId": 2,
        "warehouseName": "辅助库房",
        "maintenanceType": "special",
        "maintenanceTypeName": "专项保养",
        "maintenanceDate": "2024-01-10",
        "maintenanceContent": "更换天线，调试频率",
        "maintenanceResult": "repair",
        "maintenanceResultName": "需维修",
        "operatorId": 1,
        "operatorName": "王五",
        "nextMaintenanceDate": "2024-03-10",
        "remark": "发现频率异常",
        "createTime": "2024-01-10T14:30:00"
      }
    ]
  }
}
```

### 2. 获取保养记录详情

**接口路径**: `GET /api/maintenance/{id}`  
**接口描述**: 根据记录ID获取详细信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 保养记录ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取保养记录详情成功",
  "data": {
    "id": 1,
    "materialId": 1,
    "materialCode": "WZ001",
    "materialName": "手电筒",
    "warehouseId": 1,
    "warehouseName": "主库房",
    "maintenanceType": "routine",
    "maintenanceTypeName": "例行保养",
    "maintenanceDate": "2024-01-15",
    "maintenanceContent": "清洁外壳，检查电池",
    "maintenanceResult": "normal",
    "maintenanceResultName": "正常",
    "operatorId": 1,
    "operatorName": "王五",
    "nextMaintenanceDate": "2024-04-15",
    "remark": "设备正常",
    "createTime": "2024-01-15T10:00:00",
    "updateTime": "2024-01-15T10:00:00",
    "createBy": "maintenance_user",
    "updateBy": "maintenance_user"
  }
}
```

### 3. 创建保养记录

**接口路径**: `POST /api/maintenance/create`  
**接口描述**: 新增物资保养记录  

**请求体**:
```json
{
  "materialId": 1,
  "warehouseId": 1,
  "maintenanceType": "routine",
  "maintenanceDate": "2024-01-20",
  "maintenanceContent": "例行保养检查",
  "maintenanceResult": "normal",
  "operatorId": 1,
  "operatorName": "保养员",
  "nextMaintenanceDate": "2024-04-20",
  "remark": "保养完成"
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| materialId | Long | 是 | 物资ID |
| warehouseId | Long | 是 | 库室ID |
| maintenanceType | String | 是 | 保养类型（routine/special/emergency） |
| maintenanceDate | LocalDate | 是 | 保养日期 |
| maintenanceContent | String | 是 | 保养内容 |
| maintenanceResult | String | 是 | 保养结果（normal/abnormal/repair） |
| operatorId | Long | 是 | 保养员ID |
| operatorName | String | 是 | 保养员姓名 |
| nextMaintenanceDate | LocalDate | 否 | 下次保养日期 |
| remark | String | 否 | 备注说明 |

**响应示例**:
```json
{
  "code": 200,
  "message": "创建保养记录成功",
  "data": null
}
```

### 4. 更新保养记录

**接口路径**: `PUT /api/maintenance/{id}`  
**接口描述**: 修改保养记录信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 保养记录ID |

**请求体**: 同创建接口

**响应示例**:
```json
{
  "code": 200,
  "message": "更新保养记录成功",
  "data": null
}
```

### 5. 删除保养记录

**接口路径**: `DELETE /api/maintenance/{id}`  
**接口描述**: 删除保养记录  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 保养记录ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "删除保养记录成功",
  "data": null
}
```

### 6. 获取物资历史保养记录

**接口路径**: `GET /api/maintenance/material/{materialId}/history`  
**接口描述**: 查看指定物资的所有保养记录  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| materialId | Long | 是 | 物资ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取保养历史成功",
  "data": [
    {
      "id": 1,
      "maintenanceType": "routine",
      "maintenanceTypeName": "例行保养",
      "maintenanceDate": "2024-01-15",
      "maintenanceContent": "清洁外壳，检查电池",
      "maintenanceResult": "normal",
      "maintenanceResultName": "正常",
      "operatorName": "王五",
      "nextMaintenanceDate": "2024-04-15",
      "remark": "设备正常",
      "createTime": "2024-01-15T10:00:00"
    },
    {
      "id": 5,
      "maintenanceType": "routine",
      "maintenanceTypeName": "例行保养",
      "maintenanceDate": "2023-10-15",
      "maintenanceContent": "清洁保养，功能检测",
      "maintenanceResult": "normal",
      "maintenanceResultName": "正常",
      "operatorName": "张三",
      "nextMaintenanceDate": "2024-01-15",
      "remark": "设备正常",
      "createTime": "2023-10-15T09:00:00"
    }
  ]
}
```

### 7. 获取保养提醒列表

**接口路径**: `GET /api/maintenance/reminder`  
**接口描述**: 获取需要保养的物资列表  

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取保养提醒成功",
  "data": [
    {
      "materialId": 3,
      "materialCode": "WZ003",
      "materialName": "对讲机",
      "warehouseId": 2,
      "warehouseName": "辅助库房",
      "lastMaintenanceDate": "2024-01-10",
      "nextMaintenanceDate": "2024-03-10",
      "daysDue": 5,
      "reminderLevel": "warning",
      "reminderMessage": "距离下次保养还有5天"
    },
    {
      "materialId": 5,
      "materialCode": "WZ005",
      "materialName": "灭火器",
      "warehouseId": 1,
      "warehouseName": "主库房",
      "lastMaintenanceDate": "2023-12-15",
      "nextMaintenanceDate": "2024-01-15",
      "daysDue": -5,
      "reminderLevel": "urgent",
      "reminderMessage": "保养已逾期5天"
    }
  ]
}
```

**提醒字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| daysDue | Integer | 距离保养日期天数（负数表示逾期） |
| reminderLevel | String | 提醒级别（info/warning/urgent） |
| reminderMessage | String | 提醒消息 |

**提醒级别说明**:
- `info`: 距离保养日期15天以上
- `warning`: 距离保养日期15天以内
- `urgent`: 已逾期或今日需要保养

---

## 枚举值说明

### 保养类型 (maintenanceType)
- `routine`: 例行保养 - 定期进行的常规保养
- `special`: 专项保养 - 针对特定问题的专项保养
- `emergency`: 应急保养 - 紧急故障处理

### 保养结果 (maintenanceResult)
- `normal`: 正常 - 设备状态良好，无异常
- `abnormal`: 异常 - 发现问题但可继续使用
- `repair`: 需维修 - 设备需要专业维修

---

## 业务规则

### 保养计划
1. **保养周期**: 根据物资类型制定不同的保养周期
2. **提前提醒**: 提前15天开始提醒保养
3. **逾期处理**: 逾期保养需要说明原因
4. **保养记录**: 每次保养必须记录详细内容

### 数据约束
1. **保养日期**: 不能晚于当前日期
2. **下次保养**: 应该晚于当前保养日期
3. **保养内容**: 必须填写具体的保养内容
4. **操作员**: 必须是有效的保养人员

### 权限控制
1. **创建权限**: 只有保养人员可以创建记录
2. **修改权限**: 只能修改自己创建的记录
3. **删除权限**: 需要管理员权限
4. **查看权限**: 所有用户可查看保养记录

---

## 保养流程

### 标准流程
1. **保养计划**: 根据物资特性制定保养计划
2. **提醒通知**: 系统自动生成保养提醒
3. **执行保养**: 保养人员按计划执行保养
4. **记录填写**: 详细记录保养过程和结果
5. **结果处理**: 根据保养结果安排后续处理

### 异常处理
1. **保养异常**: 及时记录异常情况和处理措施
2. **设备故障**: 需要维修的设备标记状态
3. **逾期保养**: 分析逾期原因，调整保养计划
4. **应急保养**: 紧急情况下的保养处理流程

---

## 使用建议

### 保养管理
- 建立完善的保养计划体系
- 定期培训保养人员操作技能
- 及时处理保养提醒，避免逾期

### 数据分析
- 分析保养频率和效果
- 统计设备故障率变化
- 优化保养周期和内容

### 质量控制
- 严格执行保养标准
- 定期检查保养质量
- 建立保养效果评估机制

### 记录管理
- 保养记录要详细准确
- 及时更新保养状态
- 定期备份保养数据
