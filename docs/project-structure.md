# 项目结构说明

## 整体架构

```
src/main/java/com/fadun/
├── common/                 # 公共组件包
│   ├── enums/             # 枚举类
│   │   └── ResultCode.java        # 统一状态码枚举
│   ├── exception/         # 异常处理
│   │   ├── BusinessException.java # 业务异常类
│   │   └── GlobalExceptionHandler.java # 全局异常处理器
│   ├── result/            # 返回结果封装
│   │   ├── Result.java            # 统一返回体
│   │   └── PageResult.java        # 分页返回体
│   └── utils/             # 工具类
│       └── ResultUtils.java       # 返回体工具类
├── config/                # 配置类
│   ├── Knife4jConfig.java         # API文档配置
│   ├── MybatisPlusConfig.java     # MyBatis Plus配置
│   ├── SecurityConfig.java        # 安全配置
│   └── WebConfig.java             # Web配置
├── controller/            # 控制器层
│   ├── HelloController.java       # 系统管理接口
│   ├── UserController.java        # 用户管理接口
│   ├── MaterialController.java    # 物资台账管理
│   ├── MaterialCategoryController.java # 物资分类管理
│   ├── InboundController.java     # 物资入库管理
│   ├── OutboundController.java    # 物资出库管理
│   ├── StockController.java       # 库存管理
│   ├── MaintenanceController.java # 保养记录管理
│   └── OverviewController.java    # 模块总览
├── entity/                # 实体类
│   ├── Warehouse.java             # 库室信息
│   ├── Material.java              # 物资信息
│   ├── MaterialCategory.java      # 物资分类（简化版）
│   ├── MaterialStock.java         # 物资库存
│   ├── InboundOrder.java          # 入库单
│   ├── InboundDetail.java         # 入库单明细
│   ├── OutboundOrder.java         # 出库单
│   ├── OutboundDetail.java        # 出库单明细
│   └── MaintenanceRecord.java     # 保养记录
├── mapper/                # 数据访问层
│   ├── WarehouseMapper.java       # 库室信息Mapper
│   ├── MaterialMapper.java        # 物资信息Mapper
│   ├── MaterialCategoryMapper.java # 物资分类Mapper
│   ├── MaterialStockMapper.java   # 物资库存Mapper
│   ├── InboundOrderMapper.java    # 入库单Mapper
│   ├── OutboundOrderMapper.java   # 出库单Mapper
│   └── MaintenanceRecordMapper.java # 保养记录Mapper
├── service/               # 服务接口层
│   ├── MaterialService.java       # 物资管理服务
│   ├── MaterialCategoryService.java # 物资分类服务
│   ├── InboundService.java        # 入库管理服务
│   ├── OutboundService.java       # 出库管理服务
│   ├── StockService.java          # 库存管理服务
│   └── MaintenanceService.java    # 保养记录服务
├── service/impl/          # 服务实现层
│   ├── MaterialServiceImpl.java   # 物资管理服务实现
│   ├── MaterialCategoryServiceImpl.java # 物资分类服务实现
│   ├── InboundServiceImpl.java    # 入库管理服务实现
│   ├── OutboundServiceImpl.java   # 出库管理服务实现
│   ├── StockServiceImpl.java      # 库存管理服务实现
│   └── MaintenanceServiceImpl.java # 保养记录服务实现
└── FadunApplication.java  # 主启动类
```

## 包说明

### common包 - 公共组件
- **enums**: 枚举类，包含系统状态码等
- **exception**: 异常处理相关类
- **result**: 返回结果封装类
- **utils**: 工具类

### config包 - 配置类
- 各种Spring配置类，包括API文档、数据库、安全等配置

### controller包 - 控制器层
- RESTful API接口，负责接收请求和返回响应

### entity包 - 实体类
- 数据库表对应的实体类，使用MyBatis Plus注解
- **MaterialCategory**: 简化版，无时间字段和状态字段

### mapper包 - 数据访问层
- MyBatis Plus的Mapper接口，负责数据库操作

### service包 - 服务层
- 业务逻辑接口和实现，分离接口和实现

## 设计原则

1. **分层清晰**: Controller -> Service -> Mapper 三层架构
2. **职责单一**: 每个类只负责一个特定功能
3. **依赖注入**: 使用Spring的依赖注入管理对象
4. **统一规范**: 统一的返回格式、异常处理、命名规范

## 已删除的类

1. **ApiResult.java** - 与Result类重复，已删除
2. **MaterialStockDTO.java** - 包含已删除的金额字段，未被使用，已删除
3. **TraceFilter.java** - 请求追踪过滤器，根据需求已删除
4. **readiness包** - 旧的包结构，已完全删除
5. **filter包** - 删除TraceFilter后为空包，已删除

## 优化说明

1. **去除冗余**: 删除了重复和未使用的类
2. **结构清晰**: 保持了清晰的包结构和职责分离
3. **易于维护**: 每个包的职责明确，便于后续维护和扩展
4. **简化设计**: MaterialCategory去除时间和状态字段，保持简洁
