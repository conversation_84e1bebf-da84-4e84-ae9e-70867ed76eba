# 前后端接口文档规范

## 1. 接口文档基础规范

### 1.1 文档管理工具
- 使用Swagger、Apifox或Yapi等专业API文档管理工具
- 前后端统一使用同一个API文档平台
- 支持在线调试功能
- 支持导出多种格式(JSON、Markdown等)
- 支持版本控制

### 1.2 文档组织结构
- 按业务模块或功能模块分组组织接口
- 接口分组层次不超过3层
- 同一资源的不同操作应归类在同一组下
- 使用清晰的接口分类命名，如：用户管理、订单管理
- 接口按照业务流程顺序排列

### 1.3 接口命名规范
- 使用HTTP方法语义明确接口操作类型
  - GET：获取资源
  - POST：创建资源
  - PUT：更新资源(全量更新)
  - PATCH：部分更新资源
  - DELETE：删除资源
- 接口路径使用名词复数形式，如: /api/users, /api/orders
- 使用kebab-case(短横线)命名URL路径，如: /api/order-items
- RESTful风格路径规范：
  - 列表：/api/users
  - 详情：/api/users/{id}
  - 子资源：/api/users/{id}/orders
- 接口版本通过URL路径前缀区分，如: /api/v1/users

## 2. 接口文档内容规范

### 2.1 基本信息规范
每个接口文档必须包含以下基本信息：
- 接口名称：简短描述接口用途
- 接口描述：详细说明接口功能和使用场景
- 接口URL：完整的请求路径
- 请求方法：GET、POST、PUT、DELETE等
- 开发负责人：接口开发者
- 接口状态：开发中、测试中、已发布、已废弃等
- 创建时间和更新时间

### 2.2 请求参数规范
- 参数分类：路径参数、查询参数、请求体参数、请求头参数
- 参数属性说明：
  - 参数名称
  - 参数类型
  - 是否必填
  - 默认值
  - 参数描述
  - 参数示例
  - 参数约束(长度、范围、枚举等)
- 复杂参数使用示例JSON说明
- 数组类型参数说明其子项格式
- 文件上传参数说明格式和大小限制

### 2.3 响应结果规范
- 统一的响应结构：
  ```json
  {
    "code": 0,          // 业务状态码，0表示成功
    "message": "成功",  // 状态描述
    "data": {}          // 业务数据
  }
  ```
- 成功响应示例(至少一个)
- 失败响应示例(至少包含常见错误场景)
- 分页响应结构统一：
  ```json
  {
    "code": 0,
    "message": "成功",
    "data": {
      "list": [],       // 数据列表
      "total": 0,       // 总记录数
      "page": 1,        // 当前页码
      "pageSize": 10    // 每页条数
    }
  }
  ```
- 响应字段说明：
  - 字段名称
  - 字段类型
  - 字段描述
  - 示例值
  - 备注说明
- 层级较深的嵌套对象需要清晰注释

### 2.4 错误码规范
- 定义统一的错误码体系
- 错误码按模块或功能划分范围
- 错误码表包含：
  - 错误码
  - 错误信息
  - 错误说明
  - 处理建议
- HTTP状态码使用标准：
  - 200：成功
  - 400：客户端错误
  - 401：未授权
  - 403：权限不足
  - 404：资源不存在
  - 500：服务器错误
- 业务错误码推荐结构：模块代码(2位)+错误类型(2位)+序号(2位)

### 2.5 接口示例
- 提供完整的请求和响应示例
- 复杂接口提供多种场景的示例
- 示例数据应尽量真实有效
- 示例中的敏感信息需脱敏处理

## 3. 接口文档维护规范

### 3.1 文档更新流程
- 接口变更必须先更新文档再进行开发
- 接口文档纳入代码审核流程
- 使用自动化工具从代码注释生成文档
- 文档更新记录变更日志
- 接口弃用流程：
  1. 标记为"已废弃"状态
  2. 添加替代接口说明
  3. 保留文档至少一个版本周期

### 3.2 文档评审规范
- 新接口文档必须经过前后端评审
- 评审内容包括：
  - 接口设计合理性
  - 参数命名和类型是否合适
  - 是否符合RESTful规范
  - 文档完整性和准确性
  - 安全性检查
- 评审不通过的接口不允许开发

### 3.3 文档版本控制
- 接口文档需要版本控制
- 主版本号变更：不兼容的API修改
- 次版本号变更：向下兼容的功能性新增
- 修订号变更：向下兼容的问题修正
- 保留历史版本文档，便于查阅

## 4. 接口开发规范

### 4.1 参数命名规范
- 参数名使用camelCase(小驼峰)命名
- 参数名应见名知义，不使用缩写
- 布尔类型参数使用is、has、can等前缀
- 时间类型参数使用time、date、datetime等后缀
- ID类型参数统一命名为entityId，如userId、orderId
- 模糊查询参数使用keyword命名
- 筛选条件使用filter前缀，如filterStatus

### 4.2 数据类型规范
- 字符串：String
- 整数：Integer, Long
- 浮点数：Float, Double, Decimal
- 布尔值：Boolean
- 日期时间：Date, DateTime, Timestamp
- 数组：Array
- 对象：Object
- 文件：File, MultipartFile
- 明确声明字符串长度限制
- 明确声明数值范围

### 4.3 接口安全规范
- 敏感接口增加权限说明
- 敏感数据传输加密要求
- 接口限流、防刷说明
- 文件上传大小和格式限制
- 跨域访问策略说明
- 接口幂等性说明

## 5. 前后端协作规范

### 5.1 接口约定流程
1. 需求分析：前后端共同参与
2. 接口设计：后端主导，前端参与
3. 接口文档编写：后端编写初稿
4. 接口评审：前后端共同评审
5. 接口实现：后端开发
6. 接口联调：前后端共同完成
7. 接口测试：测试团队验证

### 5.2 接口变更流程
1. 提出变更需求
2. 评估变更影响
3. 通知相关方
4. 更新接口文档
5. 实施变更
6. 变更验证
7. 发布变更

### 5.3 接口联调规范
- 使用Mock数据辅助前端开发
- 接口实现进度实时更新
- 问题反馈与跟踪机制
- 联调环境稳定性保障
- 跨团队协作沟通机制

## 6. 特殊场景处理规范

### 6.1 文件上传下载接口
- 上传接口说明：
  - 支持的文件类型
  - 文件大小限制
  - 上传方式(formData, base64等)
  - 分片上传参数
  - 断点续传机制
- 下载接口说明：
  - 下载方式
  - 文件命名规则
  - 异常处理机制

### 6.2 导入导出接口
- 导入接口说明：
  - 模板下载
  - 导入格式要求
  - 字段映射关系
  - 校验规则
  - 失败处理机制
- 导出接口说明：
  - 导出格式
  - 大数据量处理机制
  - 导出进度查询

### 6.3 异步处理接口
- 任务提交接口
- 进度查询接口
- 结果获取接口
- 超时和失败处理机制

### 6.4 第三方接口对接
- 第三方接口文档引用
- 接口转发规则
- 异常处理机制
- 数据格式转换说明

## 7. 最佳实践

### 7.1 接口设计原则
- 单一职责：一个接口只做一件事
- 幂等性：相同请求多次执行结果一致
- 向后兼容：新版本不影响旧客户端
- 合适粒度：既不过细也不过粗
- 可测试性：便于自动化测试

### 7.2 接口优化建议
- 减少请求次数：合并相关数据
- 减少返回数据：按需返回字段
- 分页机制优化：游标分页vs偏移分页
- 批量操作接口：减少网络交互
- 缓存策略：合理利用缓存

### 7.3 常见问题解决方案
- 跨域问题处理
- 大数据量分页查询优化
- 复杂查询条件处理
- 敏感信息处理
- 高并发接口设计 