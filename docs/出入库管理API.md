# 出入库管理 API

## 模块概述

出入库管理模块包含物资入库、物资出库和出入库明细管理，提供完整的物资流转管理功能。

**包含子模块**:
- 物资入库管理 (`/api/inbound`)
- 物资出库管理 (`/api/outbound`)
- 入库明细管理 (`/api/inbound-detail`)
- 出库明细管理 (`/api/outbound-detail`)

---

## 物资入库管理

### 1. 分页查询入库单

**接口路径**: `GET /api/inbound/page`  
**接口描述**: 获取入库单列表，支持按类型和状态筛选  

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| keyword | String | 否 | - | 搜索关键词（入库单号/供应商） |
| inboundType | String | 否 | - | 入库类型 |
| status | Integer | 否 | - | 状态 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询入库单成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 45,
    "records": [
      {
        "id": 1,
        "orderNo": "RK202401001",
        "warehouseId": 1,
        "warehouseName": "主库房",
        "supplier": "某某公司",
        "inboundType": "purchase",
        "totalQuantity": 100,
        "operatorId": 1,
        "operatorName": "李四",
        "inboundTime": "2024-01-15T09:30:00",
        "status": 2,
        "statusName": "已入库",
        "remark": "采购入库",
        "createTime": "2024-01-15T09:00:00"
      }
    ]
  }
}
```

### 2. 获取入库单详情

**接口路径**: `GET /api/inbound/{id}`  
**接口描述**: 根据入库单ID获取详细信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 入库单ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取入库单详情成功",
  "data": {
    "id": 1,
    "orderNo": "RK202401001",
    "warehouseId": 1,
    "warehouseName": "主库房",
    "supplier": "某某公司",
    "inboundType": "purchase",
    "totalQuantity": 100,
    "operatorId": 1,
    "operatorName": "李四",
    "inboundTime": "2024-01-15T09:30:00",
    "status": 2,
    "statusName": "已入库",
    "remark": "采购入库",
    "createTime": "2024-01-15T09:00:00",
    "details": [
      {
        "id": 1,
        "materialId": 1,
        "materialName": "手电筒",
        "quantity": 50,
        "remark": ""
      },
      {
        "id": 2,
        "materialId": 2,
        "materialName": "电池",
        "quantity": 50,
        "remark": ""
      }
    ]
  }
}
```

### 3. 创建入库单

**接口路径**: `POST /api/inbound/create`  
**接口描述**: 新建物资入库工单  

**请求体**:
```json
{
  "warehouseId": 1,
  "supplier": "供应商名称",
  "inboundType": "purchase",
  "totalQuantity": 50,
  "operatorId": 1,
  "operatorName": "操作员",
  "remark": "备注信息"
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| warehouseId | Long | 是 | 库室ID |
| supplier | String | 是 | 供应商名称 |
| inboundType | String | 是 | 入库类型（purchase/return/transfer） |
| totalQuantity | Integer | 是 | 总数量 |
| operatorId | Long | 是 | 操作员ID |
| operatorName | String | 是 | 操作员姓名 |
| remark | String | 否 | 备注信息 |

### 4. 确认入库

**接口路径**: `POST /api/inbound/{id}/confirm`  
**接口描述**: 确认物资入库操作，更新库存  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 入库单ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "确认入库成功",
  "data": null
}
```

### 5. 取消入库

**接口路径**: `POST /api/inbound/{id}/cancel`  
**接口描述**: 取消入库单  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 入库单ID |

---

## 物资出库管理

### 1. 分页查询出库单

**接口路径**: `GET /api/outbound/page`  
**接口描述**: 获取出库单列表，支持按类型和状态筛选  

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| keyword | String | 否 | - | 搜索关键词（出库单号/接收单位/接收人） |
| outboundType | String | 否 | - | 出库类型 |
| status | Integer | 否 | - | 状态 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询出库单成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 38,
    "records": [
      {
        "id": 1,
        "orderNo": "CK202401001",
        "warehouseId": 1,
        "warehouseName": "主库房",
        "outboundType": "use",
        "receiverUnit": "安保部",
        "receiverName": "王五",
        "receiverPhone": "13800138003",
        "totalQuantity": 30,
        "operatorId": 1,
        "operatorName": "李四",
        "outboundTime": "2024-01-20T14:30:00",
        "status": 2,
        "statusName": "已出库",
        "remark": "日常使用",
        "createTime": "2024-01-20T14:00:00"
      }
    ]
  }
}
```

### 2. 获取出库单详情

**接口路径**: `GET /api/outbound/{id}`  
**接口描述**: 根据出库单ID获取详细信息  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 出库单ID |

### 3. 创建出库单

**接口路径**: `POST /api/outbound/create`  
**接口描述**: 新建物资出库工单  

**请求体**:
```json
{
  "warehouseId": 1,
  "outboundType": "use",
  "receiverUnit": "接收单位",
  "receiverName": "接收人",
  "receiverPhone": "13800138000",
  "totalQuantity": 30,
  "operatorId": 1,
  "operatorName": "操作员",
  "remark": "使用出库"
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| warehouseId | Long | 是 | 库室ID |
| outboundType | String | 是 | 出库类型（use/transfer/scrap） |
| receiverUnit | String | 是 | 接收单位 |
| receiverName | String | 是 | 接收人 |
| receiverPhone | String | 否 | 接收人电话 |
| totalQuantity | Integer | 是 | 总数量 |
| operatorId | Long | 是 | 操作员ID |
| operatorName | String | 是 | 操作员姓名 |
| remark | String | 否 | 备注信息 |

### 4. 确认出库

**接口路径**: `POST /api/outbound/{id}/confirm`  
**接口描述**: 确认物资出库操作，更新库存  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 出库单ID |

### 5. 取消出库

**接口路径**: `POST /api/outbound/{id}/cancel`  
**接口描述**: 取消出库单  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 出库单ID |

---

## 入库明细管理

### 1. 查询入库明细

**接口路径**: `GET /api/inbound-detail/order/{orderId}`  
**接口描述**: 根据入库单ID查询明细列表  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 入库单ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询入库明细成功",
  "data": [
    {
      "id": 1,
      "orderId": 1,
      "materialId": 1,
      "materialCode": "WZ001",
      "materialName": "手电筒",
      "specification": "LED强光手电筒",
      "unit": "个",
      "quantity": 50,
      "remark": ""
    },
    {
      "id": 2,
      "orderId": 1,
      "materialId": 2,
      "materialCode": "WZ002",
      "materialName": "电池",
      "specification": "AA碱性电池",
      "unit": "节",
      "quantity": 50,
      "remark": ""
    }
  ]
}
```

### 2. 批量保存入库明细

**接口路径**: `POST /api/inbound-detail/batch`  
**接口描述**: 批量保存入库明细  

**请求体**:
```json
[
  {
    "orderId": 1,
    "materialId": 1,
    "quantity": 50,
    "remark": ""
  },
  {
    "orderId": 1,
    "materialId": 2,
    "quantity": 50,
    "remark": ""
  }
]
```

### 3. 删除入库明细

**接口路径**: `DELETE /api/inbound-detail/order/{orderId}`  
**接口描述**: 删除入库单的所有明细  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 入库单ID |

---

## 出库明细管理

### 1. 查询出库明细

**接口路径**: `GET /api/outbound-detail/order/{orderId}`  
**接口描述**: 根据出库单ID查询明细列表  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 出库单ID |

### 2. 批量保存出库明细

**接口路径**: `POST /api/outbound-detail/batch`  
**接口描述**: 批量保存出库明细  

**请求体**:
```json
[
  {
    "orderId": 1,
    "materialId": 1,
    "quantity": 20,
    "remark": ""
  },
  {
    "orderId": 1,
    "materialId": 2,
    "quantity": 10,
    "remark": ""
  }
]
```

### 3. 删除出库明细

**接口路径**: `DELETE /api/outbound-detail/order/{orderId}`  
**接口描述**: 删除出库单的所有明细  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 出库单ID |

---

## 枚举值说明

### 入库类型 (inboundType)
- `purchase`: 采购入库
- `return`: 退货入库
- `transfer`: 调拨入库

### 出库类型 (outboundType)
- `use`: 使用出库
- `transfer`: 调拨出库
- `scrap`: 报废出库

### 单据状态 (status)
- **入库单/出库单**: 
  - `1`: 待处理
  - `2`: 已完成
  - `3`: 已取消

---

## 业务规则

### 入库流程
1. **创建入库单**: 生成入库单，状态为"待入库"
2. **添加明细**: 添加具体的物资明细信息
3. **确认入库**: 确认后更新库存，状态变为"已入库"
4. **取消入库**: 可取消待入库的单据

### 出库流程
1. **创建出库单**: 生成出库单，状态为"待出库"
2. **添加明细**: 添加具体的物资明细信息
3. **库存检查**: 确认前检查库存是否充足
4. **确认出库**: 确认后扣减库存，状态变为"已出库"
5. **取消出库**: 可取消待出库的单据

### 数据约束
1. **单据编号**: 系统自动生成，格式为RK/CK+年月日+序号
2. **明细数量**: 必须大于0
3. **库存检查**: 出库时检查库存是否充足
4. **状态控制**: 已完成的单据不能修改或删除

---

## 使用建议

### 操作流程
1. **标准流程**: 创建单据 → 添加明细 → 确认操作
2. **批量操作**: 使用批量接口提高效率
3. **状态检查**: 操作前检查单据状态

### 数据管理
- 及时处理待处理单据，避免积压
- 定期清理历史数据，保持系统性能
- 做好数据备份，确保数据安全

### 异常处理
- 操作失败时检查库存和单据状态
- 网络异常时避免重复提交
- 及时处理异常单据，保证数据一致性
