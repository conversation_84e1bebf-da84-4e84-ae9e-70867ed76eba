# 库存管理 API

## 模块概述

库存管理模块提供物资库存查询、统计分析和盘点管理功能，包含库存信息展示和库存操作。

**包含子模块**:
- 库存查询管理 (`/api/stock`)
- 物资库存查询 (`/api/material-stock`)

---

## 库存查询管理

### 1. 分页查询库存信息

**接口路径**: `GET /api/stock/page`  
**接口描述**: 获取物资库存信息  

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| keyword | String | 否 | - | 搜索关键词（物资名称/编码） |
| warehouseId | Long | 否 | - | 库室ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询库存信息成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 200,
    "records": [
      {
        "id": 1,
        "materialId": 1,
        "materialCode": "WZ001",
        "materialName": "手电筒",
        "specification": "LED强光手电筒",
        "unit": "个",
        "warehouseId": 1,
        "warehouseName": "主库房",
        "currentStock": 40,
        "location": "A区-01架",
        "lastInTime": "2024-01-15T09:30:00",
        "lastOutTime": "2024-01-20T14:20:00",
        "updateTime": "2024-01-20T14:20:00"
      },
      {
        "id": 2,
        "materialId": 2,
        "materialCode": "WZ002",
        "materialName": "电池",
        "specification": "AA碱性电池",
        "unit": "节",
        "warehouseId": 1,
        "warehouseName": "主库房",
        "currentStock": 310,
        "location": "A区-02架",
        "lastInTime": "2024-01-15T09:30:00",
        "lastOutTime": "2024-01-20T14:20:00",
        "updateTime": "2024-01-20T14:20:00"
      }
    ]
  }
}
```

### 2. 获取库存统计信息

**接口路径**: `GET /api/stock/statistics`  
**接口描述**: 获取库存总览和统计数据  

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| warehouseId | Long | 否 | 库室ID，不传则查询所有库室 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取库存统计成功",
  "data": {
    "totalMaterials": 156,
    "totalStock": 2580,
    "warehouseCount": 5,
    "lowStockCount": 8,
    "zeroStockCount": 2,
    "utilizationRate": 78.5,
    "warehouseStats": [
      {
        "warehouseId": 1,
        "warehouseName": "主库房",
        "materialCount": 45,
        "totalStock": 1580,
        "utilizationRate": 85.2
      },
      {
        "warehouseId": 2,
        "warehouseName": "辅助库房",
        "materialCount": 28,
        "totalStock": 890,
        "utilizationRate": 68.7
      }
    ],
    "categoryStats": [
      {
        "categoryId": 1,
        "categoryName": "防护用品",
        "materialCount": 25,
        "totalStock": 680
      },
      {
        "categoryId": 2,
        "categoryName": "照明设备",
        "materialCount": 15,
        "totalStock": 450
      }
    ]
  }
}
```

**统计字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| totalMaterials | Integer | 物资种类总数 |
| totalStock | Integer | 库存总量 |
| warehouseCount | Integer | 库室总数 |
| lowStockCount | Integer | 低库存物资数量 |
| zeroStockCount | Integer | 零库存物资数量 |
| utilizationRate | Double | 库存利用率(%) |
| warehouseStats | Array | 分库室统计信息 |
| categoryStats | Array | 分类别统计信息 |

### 3. 创建盘点单

**接口路径**: `POST /api/stock/inventory/create`  
**接口描述**: 生成库存盘点工单  

**请求体**:
```json
{
  "warehouseId": 1,
  "inventoryType": "full",
  "operatorId": 1,
  "operatorName": "盘点员",
  "remark": "月度盘点",
  "materialIds": [1, 2, 3]
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| warehouseId | Long | 是 | 库室ID |
| inventoryType | String | 是 | 盘点类型（full-全盘，partial-部分盘点） |
| operatorId | Long | 是 | 操作员ID |
| operatorName | String | 是 | 操作员姓名 |
| remark | String | 否 | 备注说明 |
| materialIds | Array | 否 | 指定盘点的物资ID列表（部分盘点时必填） |

**响应示例**:
```json
{
  "code": 200,
  "message": "创建盘点单成功",
  "data": null
}
```

### 4. 更新盘点结果

**接口路径**: `POST /api/stock/inventory/update`  
**接口描述**: 录入实际盘点数量  

**请求体**:
```json
{
  "inventoryId": 1,
  "results": [
    {
      "materialId": 1,
      "systemStock": 40,
      "actualStock": 38,
      "difference": -2,
      "remark": "发现2个损坏"
    },
    {
      "materialId": 2,
      "systemStock": 310,
      "actualStock": 310,
      "difference": 0,
      "remark": ""
    }
  ]
}
```

**字段说明**:
| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| inventoryId | Long | 是 | 盘点单ID |
| results | Array | 是 | 盘点结果列表 |
| results.materialId | Long | 是 | 物资ID |
| results.systemStock | Integer | 是 | 系统库存数量 |
| results.actualStock | Integer | 是 | 实际盘点数量 |
| results.difference | Integer | 是 | 差异数量（实际-系统） |
| results.remark | String | 否 | 差异说明 |

### 5. 完成盘点

**接口路径**: `POST /api/stock/inventory/{orderId}/complete`  
**接口描述**: 完成盘点并调整库存  

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | Long | 是 | 盘点单ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "完成盘点成功",
  "data": {
    "adjustedCount": 3,
    "totalDifference": -5,
    "adjustments": [
      {
        "materialId": 1,
        "materialName": "手电筒",
        "oldStock": 40,
        "newStock": 38,
        "difference": -2
      }
    ]
  }
}
```

---

## 物资库存查询

### 1. 获取物资总库存

**接口路径**: `GET /api/material-stock/total`  
**接口描述**: 获取所有物资的总库存信息（包含所有库房的库存汇总）  

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "查询物资总库存成功",
  "data": [
    {
      "materialId": 1,
      "materialCode": "WZ001",
      "materialName": "手电筒",
      "specification": "LED强光手电筒",
      "unit": "个",
      "categoryId": 1,
      "categoryName": "照明设备",
      "totalStock": 95,
      "warehouseStocks": [
        {
          "warehouseId": 1,
          "warehouseName": "主库房",
          "currentStock": 40,
          "location": "A区-01架"
        },
        {
          "warehouseId": 2,
          "warehouseName": "辅助库房",
          "currentStock": 55,
          "location": "B区-01架"
        }
      ]
    }
  ]
}
```

### 2. 检查库存

**接口路径**: `GET /api/material-stock/check`  
**接口描述**: 检查指定物资在指定库房的库存是否充足  

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| materialId | Long | 是 | 物资ID |
| warehouseId | Long | 是 | 库房ID |
| quantity | Integer | 是 | 需要数量 |

**响应示例**:
```json
{
  "code": 200,
  "message": "检查库存完成",
  "data": {
    "sufficient": true,
    "currentStock": 40,
    "requiredQuantity": 30,
    "availableQuantity": 40,
    "shortfall": 0
  }
}
```

**返回字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| sufficient | Boolean | 库存是否充足 |
| currentStock | Integer | 当前库存 |
| requiredQuantity | Integer | 需要数量 |
| availableQuantity | Integer | 可用数量 |
| shortfall | Integer | 缺口数量（不足时显示） |

---

## 业务规则

### 库存管理
1. **实时更新**: 出入库操作实时更新库存数量
2. **负库存控制**: 系统不允许库存为负数
3. **位置管理**: 每个物资在库室中有固定存放位置
4. **历史记录**: 保留最后入库和出库时间记录

### 盘点流程
1. **创建盘点单**: 选择库室和盘点范围
2. **执行盘点**: 现场清点物资数量
3. **录入结果**: 将实际数量录入系统
4. **差异分析**: 系统自动计算差异
5. **调整库存**: 确认后更新系统库存

### 数据约束
1. **库存数量**: 必须为非负整数
2. **盘点频率**: 建议每月进行一次全盘
3. **差异处理**: 较大差异需要说明原因
4. **权限控制**: 盘点操作需要特殊权限

---

## 预警机制

### 低库存预警
- **预警条件**: 库存数量低于设定阈值
- **预警级别**: 
  - 黄色警告: 库存量 < 20
  - 红色警告: 库存量 < 10
  - 紧急警告: 库存量 = 0

### 异常库存
- **长期未动**: 超过6个月无出入库记录
- **异常增减**: 单次变动量过大
- **位置异常**: 物资位置信息缺失或错误

---

## 使用建议

### 库存监控
- 定期查看库存统计数据
- 及时处理低库存预警
- 关注库存利用率变化

### 盘点管理
- 制定定期盘点计划
- 培训盘点人员操作流程
- 建立差异处理机制

### 数据分析
- 分析库存周转率
- 优化库存结构配置
- 预测库存需求趋势
