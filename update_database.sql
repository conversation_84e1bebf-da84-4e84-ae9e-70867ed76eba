-- 更新数据库表结构，删除冻结库存字段
USE fadun_db;

-- 删除冻结库存字段
ALTER TABLE material_stock DROP COLUMN IF EXISTS available_stock;
ALTER TABLE material_stock DROP COLUMN IF EXISTS frozen_stock;

-- 删除入库明细的批次号和存放位置字段
ALTER TABLE inbound_detail DROP COLUMN IF EXISTS batch_no;
ALTER TABLE inbound_detail DROP COLUMN IF EXISTS location;

-- 删除出库明细的批次号和存放位置字段
ALTER TABLE outbound_detail DROP COLUMN IF EXISTS batch_no;
ALTER TABLE outbound_detail DROP COLUMN IF EXISTS location;

-- 清空并重新插入物资库存数据
DELETE FROM material_stock;

-- 插入物资库存数据（根据入库出库计算的实际库存）
INSERT INTO `material_stock` (`material_id`, `warehouse_id`, `current_stock`, `location`, `last_in_time`, `last_out_time`, `update_time`) VALUES
(1, 1, 40, 'A区-01架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'),  -- A4复印纸：入库50，出库10，剩余40
(2, 1, 310, 'A区-02架', '2024-01-15 09:30:00', '2024-01-20 14:20:00', '2024-01-20 14:20:00'), -- 签字笔：入库350，出库40，剩余310
(3, 2, 8, 'B区-01架', '2024-01-05 11:00:00', '2024-01-12 09:30:00', '2024-01-12 09:30:00'),   -- 笔记本电脑：入库10，出库2，剩余8
(4, 1, 3, 'C区-01架', NULL, NULL, '2024-01-01 00:00:00'),                                      -- 激光打印机：初始库存3
(5, 3, 90, 'D区-01架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'),  -- 安全帽：入库100，出库10，剩余90
(6, 3, 95, 'D区-02架', '2024-01-22 08:00:00', '2024-01-23 10:00:00', '2024-01-23 10:00:00'),  -- 防护手套：入库100，出库5，剩余95
(7, 2, 12, 'B区-02架', NULL, NULL, '2024-01-01 00:00:00'),                                     -- 对讲机：初始库存12
(8, 3, 25, 'D区-03架', NULL, NULL, '2024-01-01 00:00:00'),                                     -- 手电筒：初始库存25
(9, 1, 120, 'A区-03架', NULL, NULL, '2024-01-01 00:00:00'),                                    -- 医用口罩：初始库存120
(10, 4, 15, 'E区-01架', '2024-01-20 14:00:00', NULL, '2024-01-20 14:00:00');                   -- 灭火器：入库5，初始10，总计15
