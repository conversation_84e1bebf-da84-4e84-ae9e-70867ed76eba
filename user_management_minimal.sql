-- 用户表（包含角色类型字段）
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码',
    `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
    `phone` VARCHAR(20) COMMENT '手机号',
    `email` VARCHAR(100) COMMENT '邮箱',
    `department` VARCHAR(100) COMMENT '部门',
    `position` VARCHAR(100) COMMENT '职位',
    `role_type` VARCHAR(50) NOT NULL COMMENT '角色类型：SUPER_ADMIN,LOGISTICS_ADMIN,TRAINING_ADMIN,WORK_ADMIN,LIFE_ADMIN,NORMAL_USER',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_role_type` (`role_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ========================================
-- 插入测试数据
-- ========================================

-- 清空表数据
DELETE FROM `sys_user`;

-- 插入测试用户（6种角色）- 明文密码
INSERT INTO `sys_user` (`username`, `password`, `real_name`, `phone`, `email`, `department`, `position`, `role_type`) VALUES
-- 超级管理员
('admin', '123456', '系统管理员', '13800000001', '<EMAIL>', '信息技术部', '系统管理员', 'SUPER_ADMIN'),

-- 4个模块管理员
('logistics_admin', '123456', '战备管理员', '13800000002', '<EMAIL>', '后勤保障部', '战备管理员', 'LOGISTICS_ADMIN'),
('training_admin', '123456', '训练管理员', '13800000003', '<EMAIL>', '训练管理部', '训练管理员', 'TRAINING_ADMIN'),
('work_admin', '123456', '工作管理员', '13800000004', '<EMAIL>', '行政管理部', '工作管理员', 'WORK_ADMIN'),
('life_admin', '123456', '生活管理员', '13800000005', '<EMAIL>', '行政管理部', '生活管理员', 'LIFE_ADMIN'),

-- 普通用户
('user01', '123456', '张三', '13800000006', '<EMAIL>', '连队', '战士', 'NORMAL_USER'),
('user02', '123456', '李四', '13800000007', '<EMAIL>', '连队', '战士', 'NORMAL_USER');