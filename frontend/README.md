# 战备模块前端界面

基于 Vue 3 + Element Plus 的战备模块管理系统前端界面。

## 功能特性

### 🎯 核心功能
- **模块总览**: 数据统计、功能导航、快捷操作
- **物资分类**: 树形结构管理、拖拽排序
- **物资台账**: 完整的CRUD操作、分类筛选
- **库存管理**: 库存查询、低库存预警、库存盘点
- **入库管理**: 入库单管理、状态流转
- **出库管理**: 出库单管理、接收人信息
- **保养记录**: 保养计划、维修记录

### 🎨 界面特点
- **响应式设计**: 适配不同屏幕尺寸
- **现代化UI**: Element Plus组件库
- **直观操作**: 表格、表单、对话框等交互
- **状态管理**: Pinia状态管理
- **路由管理**: Vue Router单页应用

## 技术栈

- **Vue 3**: 渐进式JavaScript框架
- **Element Plus**: Vue 3组件库
- **Vue Router**: 官方路由管理器
- **Pinia**: Vue状态管理
- **Axios**: HTTP客户端
- **Vite**: 前端构建工具

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
cd frontend
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本
```bash
npm run build
```

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── request.js     # Axios配置
│   │   ├── material.js    # 物资相关API
│   │   ├── warehouse.js   # 库存相关API
│   │   └── maintenance.js # 保养相关API
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── views/             # 页面组件
│   │   ├── Overview.vue   # 总览页面
│   │   ├── material/      # 物资管理
│   │   │   ├── Category.vue
│   │   │   └── List.vue
│   │   ├── stock/         # 库存管理
│   │   │   └── List.vue
│   │   ├── inbound/       # 入库管理
│   │   │   └── List.vue
│   │   ├── outbound/      # 出库管理
│   │   │   └── List.vue
│   │   └── maintenance/   # 保养管理
│   │       └── List.vue
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── index.html             # HTML模板
├── package.json           # 项目配置
├── vite.config.js         # Vite配置
└── README.md              # 说明文档
```

## API接口对接

### 后端接口地址
- 开发环境: http://localhost:8080/api
- 生产环境: 根据实际部署地址配置

### 接口列表

#### 物资分类
- `GET /readiness/category/tree` - 获取分类树
- `POST /readiness/category/create` - 创建分类
- `PUT /readiness/category/{id}` - 更新分类
- `DELETE /readiness/category/{id}` - 删除分类

#### 物资台账
- `GET /readiness/material/page` - 分页查询物资
- `POST /readiness/material/create` - 创建物资
- `PUT /readiness/material/{id}` - 更新物资
- `DELETE /readiness/material/{id}` - 删除物资

#### 库存管理
- `GET /readiness/stock/page` - 分页查询库存
- `GET /readiness/stock/statistics` - 库存统计
- `GET /readiness/stock/low-stock-alert` - 低库存预警

#### 入库管理
- `GET /readiness/inbound/page` - 分页查询入库单
- `POST /readiness/inbound/create` - 创建入库单
- `POST /readiness/inbound/{id}/confirm` - 确认入库

#### 出库管理
- `GET /readiness/outbound/page` - 分页查询出库单
- `POST /readiness/outbound/create` - 创建出库单
- `POST /readiness/outbound/{id}/confirm` - 确认出库

#### 保养记录
- `GET /readiness/maintenance/page` - 分页查询保养记录
- `POST /readiness/maintenance/create` - 创建保养记录
- `PUT /readiness/maintenance/{id}` - 更新保养记录

## 开发说明

### 代理配置
开发环境下，Vite会自动将 `/api` 请求代理到后端服务器 `http://localhost:8080`

### 状态管理
使用Pinia进行状态管理，可以根据需要添加store模块

### 样式规范
- 使用Element Plus默认主题
- 自定义样式使用scoped CSS
- 响应式布局使用Element Plus栅格系统

### 组件规范
- 使用Vue 3 Composition API
- 组件命名使用PascalCase
- 文件命名使用kebab-case

## 部署说明

### 构建
```bash
npm run build
```

### 部署
将 `dist` 目录部署到Web服务器即可

### 环境配置
生产环境需要配置正确的API地址，可以通过环境变量或配置文件设置

## 功能截图

### 总览页面
- 数据统计卡片
- 功能模块导航
- 快捷操作按钮
- 最近活动时间线

### 物资分类
- 树形结构展示
- 新增/编辑/删除操作
- 层级管理
- 排序功能

### 物资台账
- 分页表格展示
- 搜索筛选
- 表单操作
- 状态管理

### 库存管理
- 库存查询
- 低库存预警
- 库存盘点
- 统计分析

### 入库/出库管理
- 单据管理
- 状态流转
- 操作记录
- 审批流程

### 保养记录
- 保养计划
- 维修记录
- 提醒功能
- 历史查询

## 注意事项

1. **后端依赖**: 前端需要后端API支持，请确保后端服务正常运行
2. **浏览器兼容**: 支持现代浏览器，建议使用Chrome、Firefox、Safari等
3. **数据格式**: 严格按照后端API返回的数据格式进行处理
4. **错误处理**: 已集成统一的错误处理机制
5. **权限控制**: 可根据需要添加用户权限验证

## 后续扩展

- [ ] 用户权限管理
- [ ] 数据导入导出
- [ ] 报表统计
- [ ] 移动端适配
- [ ] 国际化支持
- [ ] 主题切换
- [ ] 离线缓存
