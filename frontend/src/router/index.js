import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/overview'
  },
  {
    path: '/overview',
    name: 'Overview',
    component: () => import('../views/OverviewNew.vue')
  },
  {
    path: '/material/category',
    name: 'MaterialCategory',
    component: () => import('../views/material/Category.vue')
  },
  {
    path: '/material/list',
    name: 'MaterialList',
    component: () => import('../views/material/List.vue')
  },
  {
    path: '/warehouse/list',
    name: 'WarehouseList',
    component: () => import('../views/warehouse/List.vue')
  },
  {
    path: '/stock/list',
    name: 'StockList',
    component: () => import('../views/stock/List.vue')
  },
  {
    path: '/inbound/list',
    name: 'InboundList',
    component: () => import('../views/inbound/List.vue')
  },
  {
    path: '/outbound/list',
    name: 'OutboundList',
    component: () => import('../views/outbound/List.vue')
  },
  {
    path: '/maintenance/list',
    name: 'MaintenanceList',
    component: () => import('../views/maintenance/List.vue')
  },
  {
    path: '/material/manage',
    name: 'MaterialManage',
    component: () => import('../views/material/Manage.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
