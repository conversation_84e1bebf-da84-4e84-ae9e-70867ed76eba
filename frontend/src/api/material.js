import request from './request'

// 物资分类API
export const categoryApi = {
  // 获取分类树
  getTree() {
    return request.get('/category/tree')
  },

  // 获取分类列表
  getList() {
    return request.get('/category/list')
  },

  // 获取子分类
  getChildren(parentId) {
    return request.get(`/category/children/${parentId}`)
  },

  // 获取分类详情
  getDetail(id) {
    return request.get(`/category/${id}`)
  },

  // 创建分类
  create(data) {
    return request.post('/category/create', data)
  },

  // 更新分类
  update(id, data) {
    return request.put(`/category/${id}`, data)
  },

  // 删除分类
  delete(id) {
    return request.delete(`/category/${id}`)
  },

  // 检查编码
  checkCode(categoryCode, excludeId) {
    return request.get('/category/check-code', {
      params: { categoryCode, excludeId }
    })
  }
}

// 物资台账API
export const materialApi = {
  // 分页查询
  getPage(params) {
    return request.get('/material/page', { params })
  },

  // 获取详情
  getDetail(id) {
    return request.get(`/material/${id}`)
  },

  // 创建物资
  create(data) {
    return request.post('/material/create', data)
  },

  // 更新物资
  update(id, data) {
    return request.put(`/material/${id}`, data)
  },

  // 删除物资
  delete(id) {
    return request.delete(`/material/${id}`)
  }
}
