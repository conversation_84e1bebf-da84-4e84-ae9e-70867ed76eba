import request from './request'

// 库室管理API
export const warehouseApi = {
  // 分页查询库室
  getPage(params) {
    return request.get('/warehouse/page', { params })
  },

  // 获取库室列表
  getList() {
    return request.get('/warehouse/list')
  },

  // 获取库室详情
  getDetail(id) {
    return request.get(`/warehouse/${id}`)
  },

  // 创建库室
  create(data) {
    return request.post('/warehouse/create', data)
  },

  // 更新库室
  update(id, data) {
    return request.put(`/warehouse/${id}`, data)
  },

  // 删除库室
  delete(id) {
    return request.delete(`/warehouse/${id}`)
  },

  // 检查编码
  checkCode(warehouseCode, excludeId) {
    return request.get('/warehouse/check-code', {
      params: { warehouseCode, excludeId }
    })
  },

  // 获取库室统计
  getStatistics() {
    return request.get('/warehouse/statistics')
  }
}

// 库存管理API
export const stockApi = {
  // 分页查询库存
  getPage(params) {
    return request.get('/stock/page', { params })
  },

  // 获取库存统计
  getStatistics(warehouseId) {
    return request.get('/stock/statistics', {
      params: { warehouseId }
    })
  },

  // 获取低库存预警
  getLowStockAlert(warehouseId) {
    return request.get('/stock/low-stock-alert', {
      params: { warehouseId }
    })
  },

  // 创建盘点单
  createInventory(data) {
    return request.post('/stock/inventory/create', data)
  },

  // 更新盘点结果
  updateInventory(data) {
    return request.post('/stock/inventory/update', data)
  },

  // 完成盘点
  completeInventory(orderId) {
    return request.post(`/stock/inventory/${orderId}/complete`)
  }
}

// 入库管理API
export const inboundApi = {
  // 分页查询入库单
  getPage(params) {
    return request.get('/inbound/page', { params })
  },

  // 获取入库单详情
  getDetail(id) {
    return request.get(`/inbound/${id}`)
  },

  // 创建入库单
  create(data) {
    return request.post('/inbound/create', data)
  },

  // 确认入库
  confirm(id) {
    return request.post(`/inbound/${id}/confirm`)
  },

  // 取消入库
  cancel(id) {
    return request.post(`/inbound/${id}/cancel`)
  }
}

// 入库明细API
export const inboundDetailApi = {
  // 根据入库单ID查询明细
  getDetailsByOrderId(orderId) {
    return request.get(`/inbound-detail/order/${orderId}`)
  },

  // 批量保存明细
  saveDetails(details) {
    return request.post('/inbound-detail/batch', details)
  },

  // 删除入库单的所有明细
  deleteByOrderId(orderId) {
    return request.delete(`/inbound-detail/order/${orderId}`)
  }
}

// 出库管理API
export const outboundApi = {
  // 分页查询出库单
  getPage(params) {
    return request.get('/outbound/page', { params })
  },

  // 获取出库单详情
  getDetail(id) {
    return request.get(`/outbound/${id}`)
  },

  // 创建出库单
  create(data) {
    return request.post('/outbound/create', data)
  },

  // 确认出库
  confirm(id) {
    return request.post(`/outbound/${id}/confirm`)
  },

  // 取消出库
  cancel(id) {
    return request.post(`/outbound/${id}/cancel`)
  }
}

// 出库明细API
export const outboundDetailApi = {
  // 根据出库单ID查询明细
  getDetailsByOrderId(orderId) {
    return request.get(`/outbound-detail/order/${orderId}`)
  },

  // 批量保存明细
  saveDetails(details) {
    return request.post('/outbound-detail/batch', details)
  },

  // 删除出库单的所有明细
  deleteByOrderId(orderId) {
    return request.delete(`/outbound-detail/order/${orderId}`)
  }
}

// 物资库存API
export const materialStockApi = {
  // 获取物资总库存信息
  getMaterialWithTotalStock() {
    return request.get('/material-stock/total')
  },

  // 检查库存是否充足
  checkStockSufficient(materialId, warehouseId, quantity) {
    return request.get('/material-stock/check', {
      params: { materialId, warehouseId, quantity }
    })
  }
}
