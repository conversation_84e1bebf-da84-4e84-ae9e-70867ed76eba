import request from './request'

// 保养记录API
export const maintenanceApi = {
  // 分页查询保养记录
  getPage(params) {
    return request.get('/maintenance/page', { params })
  },

  // 获取保养记录详情
  getDetail(id) {
    return request.get(`/maintenance/${id}`)
  },

  // 创建保养记录
  create(data) {
    return request.post('/maintenance/create', data)
  },

  // 更新保养记录
  update(id, data) {
    return request.put(`/maintenance/${id}`, data)
  },

  // 删除保养记录
  delete(id) {
    return request.delete(`/maintenance/${id}`)
  },

  // 获取物资保养历史
  getMaterialHistory(materialId) {
    return request.get(`/maintenance/material/${materialId}/history`)
  },

  // 获取保养提醒
  getReminder() {
    return request.get('/maintenance/reminder')
  }
}
