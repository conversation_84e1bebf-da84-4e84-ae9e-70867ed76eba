<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航 -->
      <el-header class="layout-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><Box /></el-icon>
            <span>战备模块管理系统</span>
          </div>
          <div class="user-info">
            <el-dropdown>
              <span class="el-dropdown-link">
                <el-icon><User /></el-icon>
                管理员
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人设置</el-dropdown-item>
                  <el-dropdown-item divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="250px" class="layout-aside">
          <el-menu
            :default-active="$route.path"
            router
            class="el-menu-vertical"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
          >
            <el-menu-item index="/overview">
              <el-icon><DataAnalysis /></el-icon>
              <span>模块总览</span>
            </el-menu-item>
            
            <el-sub-menu index="material">
              <template #title>
                <el-icon><Box /></el-icon>
                <span>物资管理</span>
              </template>
              <el-menu-item index="/material/manage">
                <el-icon><Setting /></el-icon>
                <span>物资信息管理</span>
              </el-menu-item>
              <el-menu-item index="/material/category">
                <el-icon><Menu /></el-icon>
                <span>物资分类</span>
              </el-menu-item>
              <el-menu-item index="/material/list">
                <el-icon><Goods /></el-icon>
                <span>物资台账</span>
              </el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="warehouse">
              <template #title>
                <el-icon><House /></el-icon>
                <span>库存管理</span>
              </template>
              <el-menu-item index="/warehouse/list">
                <el-icon><House /></el-icon>
                <span>库室管理</span>
              </el-menu-item>
              <el-menu-item index="/stock/list">
                <el-icon><DataBoard /></el-icon>
                <span>库存查询</span>
              </el-menu-item>
              <el-menu-item index="/inbound/list">
                <el-icon><Upload /></el-icon>
                <span>物资入库</span>
              </el-menu-item>
              <el-menu-item index="/outbound/list">
                <el-icon><Download /></el-icon>
                <span>物资出库</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/maintenance/list">
              <el-icon><Tools /></el-icon>
              <span>保养记录</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="layout-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { Box, User, ArrowDown, DataAnalysis, Menu, Goods, House, DataBoard, Upload, Download, Tools, Setting } from '@element-plus/icons-vue'
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.layout-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.logo .el-icon {
  margin-right: 8px;
  font-size: 24px;
}

.user-info {
  color: #606266;
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.layout-aside {
  background-color: #304156;
}

.layout-main {
  background-color: #f5f5f5;
  padding: 20px;
}

.el-menu-vertical {
  border-right: none;
}
</style>
