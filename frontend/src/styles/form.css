/* 通用表单样式 */

/* 表单分组样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

/* 对话框样式 */
.dialog-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单项样式优化 */
.el-form-item__label {
  font-weight: 500 !important;
  color: #606266 !important;
}

.el-input__wrapper {
  border-radius: 6px !important;
}

.el-textarea__inner {
  border-radius: 6px !important;
}

.el-radio {
  margin-right: 20px !important;
}

.el-radio__label {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px !important;
  overflow: hidden !important;
}

.el-table__header-wrapper {
  border-radius: 8px 8px 0 0 !important;
}

.el-button-group .el-button {
  margin: 0 !important;
}

.el-button-group .el-button:not(:last-child) {
  border-right: 1px solid #dcdfe6 !important;
}

/* 搜索区域样式优化 */
.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.search-area .el-form-item {
  margin-bottom: 0 !important;
}

.search-area .el-form-item__label {
  font-weight: 500 !important;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header .el-button {
  font-weight: 500;
}

/* 分页样式 */
.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 页面容器样式 */
.page-container {
  padding: 0;
}

/* 状态标签样式 */
.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 表格空状态 */
.empty-state {
  padding: 40px;
  text-align: center;
  color: #909399;
}

/* 加载状态 */
.loading-container {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .search-area {
    padding: 16px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
