<template>
  <div class="overview-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据统计总览</h2>
      <p>系统各模块数据统计和库存状态分析</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6" v-for="(stat, key) in statistics" :key="key">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" :class="stat.iconClass">
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-value">{{ stat.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 物资库存统计图表 -->
    <el-row style="margin-top: 20px;" :gutter="20">
      <el-col :span="16">
        <el-card>
          <template #header>
            <span>物资库存分布统计</span>
          </template>
          
          <div v-loading="stockLoading" style="height: 400px;">
            <v-chart :option="stockChartOption" style="height: 100%;" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>库存状态分布</span>
          </template>
          
          <div v-loading="stockLoading" style="height: 400px;">
            <v-chart :option="statusChartOption" style="height: 100%;" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 出入库趋势统计 -->
    <el-row style="margin-top: 20px;" :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>近期出入库趋势</span>
          </template>
          
          <div style="height: 300px;">
            <v-chart :option="trendChartOption" style="height: 100%;" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>库存预警统计</span>
          </template>
          
          <div class="warning-stats">
            <div class="warning-item">
              <div class="warning-icon danger">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="warning-info">
                <div class="warning-title">库存不足</div>
                <div class="warning-count">{{ warningStats.lowStock }}</div>
              </div>
            </div>
            
            <div class="warning-item">
              <div class="warning-icon warning">
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="warning-info">
                <div class="warning-title">库存过多</div>
                <div class="warning-count">{{ warningStats.highStock }}</div>
              </div>
            </div>
            
            <div class="warning-item">
              <div class="warning-icon success">
                <el-icon><SuccessFilled /></el-icon>
              </div>
              <div class="warning-info">
                <div class="warning-title">库存正常</div>
                <div class="warning-count">{{ warningStats.normalStock }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart, PieChart, LineChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { overviewApi } from '../api/overview'
import { materialStockApi } from '../api/warehouse'
import { Box, DataAnalysis, House, Tools, Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'

// 注册ECharts组件
use([
  CanvasRenderer,
  BarChart,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const stockLoading = ref(false)
const materialStocks = ref([])

// 统计数据
const statistics = ref({
  totalMaterials: {
    title: '物资总数',
    value: 0,
    icon: Box,
    iconClass: 'stat-icon-primary'
  },
  totalStock: {
    title: '总库存量',
    value: 0,
    icon: DataAnalysis,
    iconClass: 'stat-icon-success'
  },
  warehouses: {
    title: '库房数量',
    value: 0,
    icon: House,
    iconClass: 'stat-icon-warning'
  },
  lowStockItems: {
    title: '库存预警',
    value: 0,
    icon: Tools,
    iconClass: 'stat-icon-danger'
  }
})

// 预警统计
const warningStats = computed(() => {
  const stats = { lowStock: 0, highStock: 0, normalStock: 0 }
  
  materialStocks.value.forEach(item => {
    const status = getStockStatus(item.totalStock, item.minStock, item.maxStock)
    if (status === 'low') stats.lowStock++
    else if (status === 'high') stats.highStock++
    else stats.normalStock++
  })
  
  return stats
})

// 加载物资库存信息
const loadMaterialStocks = async () => {
  try {
    stockLoading.value = true
    const response = await materialStockApi.getMaterialWithTotalStock()
    materialStocks.value = response.data || []
    
    // 更新统计数据
    updateStatistics()
  } catch (error) {
    console.error('加载物资库存失败:', error)
    ElMessage.error('加载物资库存信息失败')
  } finally {
    stockLoading.value = false
  }
}

// 更新统计数据
const updateStatistics = () => {
  const totalStock = materialStocks.value.reduce((sum, item) => sum + (item.totalStock || 0), 0)
  const lowStockCount = materialStocks.value.filter(item => 
    item.totalStock <= item.minStock
  ).length
  
  statistics.value.totalMaterials.value = materialStocks.value.length
  statistics.value.totalStock.value = totalStock
  statistics.value.warehouses.value = 5 // 固定值，可以从API获取
  statistics.value.lowStockItems.value = lowStockCount
}

// 库存分布图配置
const stockChartOption = computed(() => ({
  title: {
    text: '物资库存分布',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: function(params) {
      const data = params[0]
      const material = materialStocks.value[data.dataIndex]
      return `${data.name}<br/>
              当前库存: ${data.value}<br/>
              最低库存: ${material?.minStock || 0}<br/>
              最高库存: ${material?.maxStock || 0}<br/>
              状态: ${getStockStatusText(data.value, material?.minStock, material?.maxStock)}`
    }
  },
  xAxis: {
    type: 'category',
    data: materialStocks.value.map(item => item.materialName),
    axisLabel: {
      rotate: 45,
      interval: 0
    }
  },
  yAxis: {
    type: 'value',
    name: '库存数量'
  },
  series: [{
    name: '库存数量',
    type: 'bar',
    data: materialStocks.value.map(item => ({
      value: item.totalStock || 0,
      itemStyle: {
        color: getStockColor(item.totalStock, item.minStock, item.maxStock)
      }
    })),
    label: {
      show: true,
      position: 'top'
    }
  }]
}))

// 库存状态统计图配置
const statusChartOption = computed(() => {
  const statusData = getStockStatusData()
  return {
    title: {
      text: '库存状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: '库存状态',
      type: 'pie',
      radius: '50%',
      data: statusData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
})

// 出入库趋势图配置
const trendChartOption = computed(() => ({
  title: {
    text: '近期出入库趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['入库', '出库']
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '入库',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230],
      itemStyle: { color: '#67C23A' }
    },
    {
      name: '出库',
      type: 'line',
      data: [220, 182, 191, 234, 290, 330],
      itemStyle: { color: '#E6A23C' }
    }
  ]
}))

// 获取库存状态数据
const getStockStatusData = () => {
  const statusCount = { normal: 0, low: 0, high: 0 }
  
  materialStocks.value.forEach(item => {
    const status = getStockStatus(item.totalStock, item.minStock, item.maxStock)
    statusCount[status]++
  })
  
  return [
    { value: statusCount.normal, name: '正常', itemStyle: { color: '#67C23A' } },
    { value: statusCount.low, name: '库存不足', itemStyle: { color: '#F56C6C' } },
    { value: statusCount.high, name: '库存过多', itemStyle: { color: '#E6A23C' } }
  ]
}

// 获取库存状态
const getStockStatus = (current, min, max) => {
  if (current <= min) return 'low'
  if (current >= max) return 'high'
  return 'normal'
}

// 获取库存状态文本
const getStockStatusText = (current, min, max) => {
  if (current <= min) return '库存不足'
  if (current >= max) return '库存过多'
  return '正常'
}

// 获取库存颜色
const getStockColor = (current, min, max) => {
  if (current <= min) return '#F56C6C'
  if (current >= max) return '#E6A23C'
  return '#67C23A'
}

onMounted(() => {
  loadMaterialStocks()
})
</script>

<style scoped>
.overview-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon-primary {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-icon-success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon-warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-icon-danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.warning-stats {
  padding: 20px 0;
}

.warning-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.warning-item:last-child {
  margin-bottom: 0;
}

.warning-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.warning-icon.danger {
  background: #F56C6C;
}

.warning-icon.warning {
  background: #E6A23C;
}

.warning-icon.success {
  background: #67C23A;
}

.warning-info {
  flex: 1;
}

.warning-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.warning-count {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}
</style>
