<template>
  <div class="outbound-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>出库管理</span>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            新建出库单
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="出库单号/接收单位"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item label="出库类型">
            <el-select v-model="searchForm.outboundType" placeholder="请选择类型" style="width: 120px;" clearable>
              <el-option label="使用" value="use" />
              <el-option label="调拨" value="transfer" />
              <el-option label="报废" value="scrap" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px;" clearable>
              <el-option label="待出库" :value="1" />
              <el-option label="已出库" :value="2" />
              <el-option label="已取消" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadOutboundData">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        empty-text="暂无数据"
      >
        <el-table-column prop="orderNo" label="出库单号" width="180" align="center">
          <template #default="{ row }">
            <el-tag type="warning" size="small">{{ row.orderNo }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="outboundType" label="出库类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.outboundType)" size="small">
              {{ getTypeText(row.outboundType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="receiverUnit" label="接收单位" width="160" show-overflow-tooltip />
        <el-table-column prop="receiverName" label="接收人" width="120" align="center" />
        <el-table-column prop="receiverPhone" label="联系电话" width="140" align="center" />
        <el-table-column prop="totalQuantity" label="总数量" width="100" align="center" />
        <el-table-column prop="operatorName" label="操作员" width="120" align="center" />
        <el-table-column prop="outboundTime" label="出库时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.outboundTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="info" size="small" plain @click="viewDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button
                type="success"
                size="small"
                plain
                @click="confirmOutbound(row)"
                v-if="row.status === 1"
              >
                <el-icon><Check /></el-icon>
                确认出库
              </el-button>
              <el-button
                type="danger"
                size="small"
                plain
                @click="cancelOutbound(row)"
                v-if="row.status === 1"
              >
                <el-icon><Close /></el-icon>
                取消
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadOutboundData"
          @current-change="loadOutboundData"
        />
      </div>
    </el-card>

    <!-- 新建出库单对话框 -->
    <el-dialog
      title="新建出库单"
      v-model="dialogVisible"
      width="1200px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="库房" prop="warehouseId">
              <el-select v-model="form.warehouseId" placeholder="请选择库房" style="width: 100%;">
                <el-option
                  v-for="warehouse in warehouses"
                  :key="warehouse.id"
                  :label="warehouse.warehouseName"
                  :value="warehouse.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库类型" prop="outboundType">
              <el-select v-model="form.outboundType" placeholder="请选择类型" style="width: 100%;">
                <el-option label="使用" value="use" />
                <el-option label="调拨" value="transfer" />
                <el-option label="报废" value="scrap" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="接收单位" prop="receiverUnit">
              <el-input v-model="form.receiverUnit" placeholder="请输入接收单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收人" prop="receiverName">
              <el-input v-model="form.receiverName" placeholder="请输入接收人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="form.receiverPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员" prop="operatorName">
              <el-input v-model="form.operatorName" placeholder="请输入操作员姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="出库时间" prop="outboundTime">
          <el-date-picker
            v-model="form.outboundTime"
            type="datetime"
            placeholder="选择出库时间"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>

        <!-- 物资明细 -->
        <el-divider content-position="left">物资明细</el-divider>

        <!-- 添加物资明细 -->
        <div class="detail-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-select v-model="detailForm.materialId" placeholder="选择物资" style="width: 100%;" filterable>
                <el-option
                  v-for="material in materials"
                  :key="material.id"
                  :label="`${material.materialName} (${material.materialCode})`"
                  :value="material.id"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input-number v-model="detailForm.quantity" :min="1" placeholder="数量" style="width: 100%;" />
            </el-col>
            <el-col :span="6">
              <el-input v-model="detailForm.remark" placeholder="备注" />
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="addDetail" :disabled="!detailForm.materialId || !detailForm.quantity">
                <el-icon><Plus /></el-icon>
                添加
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 明细列表 -->
        <el-table :data="form.details" border style="margin-top: 10px;" max-height="300">
          <el-table-column prop="materialName" label="物资名称" min-width="200" />
          <el-table-column prop="materialCode" label="物资编码" width="120" />
          <el-table-column prop="quantity" label="数量" width="100" align="center" />
          <el-table-column prop="unit" label="单位" width="80" align="center" />
          <el-table-column prop="remark" label="备注" min-width="150" />
          <el-table-column label="操作" width="80" align="center">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeDetail($index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="出库单详情"
      v-model="detailDialogVisible"
      width="900px"
      :close-on-click-modal="false"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <View />
          </el-icon>
          <span>出库单详情信息</span>
        </div>
      </template>

      <div v-loading="detailLoading" class="detail-content">
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">出库单号：</span>
                <span class="value">{{ detailData.orderNo }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">出库类型：</span>
                <span class="value">{{ detailData.outboundType }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">库房：</span>
                <span class="value">{{ detailData.warehouseName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">操作员：</span>
                <span class="value">{{ detailData.operatorName }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item">
            <span class="label">出库时间：</span>
            <span class="value">{{ detailData.outboundTime }}</span>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">接收方信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">接收单位：</span>
                <span class="value">{{ detailData.receiverUnit }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">接收人：</span>
                <span class="value">{{ detailData.receiverName }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ detailData.receiverPhone || '-' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">状态信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">状态：</span>
                <el-tag :type="getStatusTagType(detailData.status)" size="small">
                  {{ getStatusText(detailData.status) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ detailData.createTime }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item" v-if="detailData.remark">
            <span class="label">备注：</span>
            <span class="value">{{ detailData.remark }}</span>
          </div>
        </div>

        <!-- 物资明细 -->
        <div class="detail-section" v-if="detailData.details && detailData.details.length > 0">
          <div class="section-title">物资明细</div>
          <el-table :data="detailData.details" border>
            <el-table-column prop="materialCode" label="物资编码" width="120" />
            <el-table-column prop="materialName" label="物资名称" min-width="200" />
            <el-table-column prop="specification" label="规格型号" width="150" />
            <el-table-column prop="quantity" label="数量" width="100" align="center" />
            <el-table-column prop="unit" label="单位" width="80" align="center" />
            <el-table-column prop="remark" label="备注" min-width="150" />
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { outboundApi, outboundDetailApi } from '../../api/warehouse'
import { materialApi } from '../../api/material'
import { warehouseApi } from '../../api/warehouse'
import { Plus, Search, View, Check, Close, Edit, Delete } from '@element-plus/icons-vue'

const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const formRef = ref()

// 物资和库房数据
const materials = ref([])
const warehouses = ref([])

const searchForm = reactive({
  keyword: '',
  outboundType: '',
  status: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const form = reactive({
  warehouseId: null,
  outboundType: '',
  receiverUnit: '',
  receiverName: '',
  receiverPhone: '',
  operatorName: '',
  outboundTime: new Date(),
  remark: '',
  details: []
})

// 物资明细表单
const detailForm = reactive({
  materialId: null,
  quantity: 1,
  remark: ''
})

const rules = {
  warehouseId: [
    { required: true, message: '请选择库房', trigger: 'change' }
  ],
  outboundType: [
    { required: true, message: '请选择出库类型', trigger: 'change' }
  ],
  receiverUnit: [
    { required: true, message: '请输入接收单位', trigger: 'blur' }
  ],
  receiverName: [
    { required: true, message: '请输入接收人', trigger: 'blur' }
  ],
  operatorName: [
    { required: true, message: '请输入操作员姓名', trigger: 'blur' }
  ],
  outboundTime: [
    { required: true, message: '请选择出库时间', trigger: 'change' }
  ]
}

const loadOutboundData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await outboundApi.getPage(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('加载出库数据失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    outboundType: '',
    status: null
  })
  pagination.current = 1
  loadOutboundData()
}

const showAddDialog = () => {
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    warehouseId: null,
    outboundType: '',
    receiverUnit: '',
    receiverName: '',
    receiverPhone: '',
    operatorName: '',
    outboundTime: new Date(),
    remark: '',
    details: []
  })
  resetDetailForm()
  formRef.value?.resetFields()
}

const resetDetailForm = () => {
  Object.assign(detailForm, {
    materialId: null,
    quantity: 1,
    remark: ''
  })
}

// 添加物资明细
const addDetail = () => {
  const material = materials.value.find(m => m.id === detailForm.materialId)
  if (!material) {
    ElMessage.warning('请选择物资')
    return
  }

  // 检查是否已存在相同物资
  const existIndex = form.details.findIndex(d => d.materialId === detailForm.materialId)
  if (existIndex >= 0) {
    ElMessage.warning('该物资已存在，请直接修改数量')
    return
  }

  form.details.push({
    materialId: detailForm.materialId,
    materialName: material.materialName,
    materialCode: material.materialCode,
    unit: material.unit,
    quantity: detailForm.quantity,
    remark: detailForm.remark
  })

  resetDetailForm()
}

// 删除物资明细
const removeDetail = (index) => {
  form.details.splice(index, 1)
}

const submitForm = async () => {
  try {
    await formRef.value.validate()

    // 验证物资明细
    if (!form.details || form.details.length === 0) {
      ElMessage.warning('请至少添加一个物资明细')
      return
    }

    submitLoading.value = true

    const response = await outboundApi.create(form)

    // 检查响应结果
    if (response.code === 200) {
      ElMessage.success(response.message || '创建成功')
      dialogVisible.value = false
      loadOutboundData()
    } else {
      // 显示库存不足等业务错误信息
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    if (error.errors) return
    // 处理网络错误等异常
    ElMessage.error(error.message || '网络错误，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 详情相关状态
const detailDialogVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

const viewDetail = async (row) => {
  try {
    detailLoading.value = true
    detailDialogVisible.value = true
    const response = await outboundApi.getDetail(row.id)
    detailData.value = response.data
  } catch (error) {
    ElMessage.error('获取详情失败')
    detailDialogVisible.value = false
  } finally {
    detailLoading.value = false
  }
}

const confirmOutbound = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认出库单"${row.orderNo}"吗？`,
      '确认出库',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await outboundApi.confirm(row.id)
    ElMessage.success('确认出库成功')
    loadOutboundData()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || '确认出库失败')
  }
}

const cancelOutbound = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消出库单"${row.orderNo}"吗？`,
      '取消出库',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await outboundApi.cancel(row.id)
    ElMessage.success('取消成功')
    loadOutboundData()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || '取消失败')
  }
}

const getTypeText = (type) => {
  const typeMap = {
    use: '使用',
    transfer: '调拨',
    scrap: '报废'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    use: 'primary',
    transfer: 'info',
    scrap: 'danger'
  }
  return typeMap[type] || ''
}

const getStatusText = (status) => {
  const statusMap = {
    1: '待出库',
    2: '已出库',
    3: '已取消'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const statusMap = {
    1: 'warning',
    2: 'success',
    3: 'danger'
  }
  return statusMap[status] || ''
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString()
}

// 加载物资列表
const loadMaterials = async () => {
  try {
    const response = await materialApi.getPage({ current: 1, size: 1000 })
    materials.value = response.data.records || []
  } catch (error) {
    console.error('加载物资列表失败:', error)
  }
}

// 加载库房列表
const loadWarehouses = async () => {
  try {
    const response = await warehouseApi.getList()
    warehouses.value = response.data || []
  } catch (error) {
    console.error('加载库房列表失败:', error)
  }
}

onMounted(() => {
  loadOutboundData()
  loadMaterials()
  loadWarehouses()
})
</script>

<style scoped>
.outbound-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 80px;
  margin: 0;
}

/* 详情对话框样式 */
.detail-content {
  min-height: 200px;
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  min-width: 100px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  word-break: break-all;
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 明细表单样式 */
.detail-form {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.detail-form .el-row {
  align-items: center;
}

.detail-form .el-input-number {
  width: 100%;
}
</style>
