<template>
  <div class="stock-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>库存管理</span>
          <div>
            <el-button type="warning" @click="showInventoryDialog">
              <el-icon><DataBoard /></el-icon>
              库存盘点
            </el-button>
            <el-button type="danger" @click="showLowStockAlert">
              <el-icon><Warning /></el-icon>
              低库存预警
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="物资名称/编码"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item label="库房">
            <el-select v-model="searchForm.warehouseId" placeholder="请选择库房" style="width: 150px;" clearable>
              <el-option label="主库房" :value="1" />
              <el-option label="备用库房" :value="2" />
              <el-option label="器材库" :value="3" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="loadStockData">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        empty-text="暂无数据"
      >
        <el-table-column prop="materialCode" label="物资编码" width="150" align="center">
          <template #default="{ row }">
            <el-tag type="success" size="small">{{ row.materialCode }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="materialName" label="物资名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="warehouseName" label="库房" width="140" align="center" />
        <el-table-column prop="location" label="存放位置" width="140" show-overflow-tooltip />
        <el-table-column prop="currentStock" label="当前库存" width="120" align="center">
          <template #default="{ row }">
            <span :class="{ 'low-stock': row.currentStock <= row.minStock }">
              <el-tag
                :type="row.currentStock <= row.minStock ? 'danger' : 'success'"
                size="small"
              >
                {{ row.currentStock }}
              </el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="availableStock" label="可用库存" width="120" align="center" />
        <el-table-column prop="frozenStock" label="冻结库存" width="120" align="center" />
        <el-table-column prop="minStock" label="最低库存" width="120" align="center" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="lastInTime" label="最后入库" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.lastInTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastOutTime" label="最后出库" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.lastOutTime) }}
          </template>
        </el-table-column>
        <el-table-column label="库存状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.currentStock <= row.minStock ? 'danger' : 'success'" size="small">
              <el-icon style="margin-right: 4px;">
                <Warning v-if="row.currentStock <= row.minStock" />
                <Check v-else />
              </el-icon>
              {{ row.currentStock <= row.minStock ? '低库存' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadStockData"
          @current-change="loadStockData"
        />
      </div>
    </el-card>

    <!-- 库存盘点对话框 -->
    <el-dialog
      title="库存盘点"
      v-model="inventoryDialogVisible"
      width="500px"
    >
      <el-form :model="inventoryForm" label-width="100px">
        <el-form-item label="盘点类型">
          <el-select v-model="inventoryForm.inventoryType" style="width: 100%;">
            <el-option label="全盘" value="full" />
            <el-option label="抽盘" value="spot" />
            <el-option label="月度盘点" value="monthly" />
          </el-select>
        </el-form-item>
        <el-form-item label="库房">
          <el-select v-model="inventoryForm.warehouseId" style="width: 100%;">
            <el-option label="主库房" :value="1" />
            <el-option label="备用库房" :value="2" />
            <el-option label="器材库" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="盘点日期">
          <el-date-picker
            v-model="inventoryForm.inventoryDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="inventoryForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="inventoryDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createInventory">创建盘点单</el-button>
      </template>
    </el-dialog>

    <!-- 低库存预警对话框 -->
    <el-dialog
      title="低库存预警"
      v-model="alertDialogVisible"
      width="800px"
    >
      <el-table :data="lowStockData" stripe>
        <el-table-column prop="materialName" label="物资名称" />
        <el-table-column prop="currentStock" label="当前库存" />
        <el-table-column prop="minStock" label="最低库存" />
        <el-table-column prop="warehouseName" label="库房" />
        <el-table-column label="缺口数量">
          <template #default="{ row }">
            <span style="color: #f56c6c;">
              {{ row.minStock - row.currentStock }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { stockApi } from '../../api/warehouse'
import { DataBoard, Warning, Search, Check } from '@element-plus/icons-vue'

const loading = ref(false)
const tableData = ref([])
const lowStockData = ref([])
const inventoryDialogVisible = ref(false)
const alertDialogVisible = ref(false)

const searchForm = reactive({
  keyword: '',
  warehouseId: null,
  lowStock: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const inventoryForm = reactive({
  inventoryType: 'monthly',
  warehouseId: null,
  inventoryDate: new Date(),
  remark: ''
})

const loadStockData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await stockApi.getPage(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('加载库存数据失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    warehouseId: null
  })
  pagination.current = 1
  loadStockData()
}

const showInventoryDialog = () => {
  inventoryDialogVisible.value = true
}



const createInventory = async () => {
  try {
    await stockApi.createInventory(inventoryForm)
    ElMessage.success('盘点单创建成功')
    inventoryDialogVisible.value = false
  } catch (error) {
    ElMessage.error('创建盘点单失败')
  }
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString()
}

onMounted(() => {
  loadStockData()
})
</script>

<style scoped>
.stock-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.low-stock {
  color: #f56c6c;
  font-weight: bold;
}
</style>
