<template>
  <div class="maintenance-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>保养记录管理</span>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            新增保养记录
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="物资名称/操作员"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item label="保养类型">
            <el-select v-model="searchForm.maintenanceType" placeholder="请选择类型" style="width: 120px;" clearable>
              <el-option label="例行保养" value="routine" />
              <el-option label="专项检查" value="special" />
              <el-option label="紧急维修" value="emergency" />
            </el-select>
          </el-form-item>
          <el-form-item label="保养结果">
            <el-select v-model="searchForm.maintenanceResult" placeholder="请选择结果" style="width: 120px;" clearable>
              <el-option label="正常" value="normal" />
              <el-option label="需维修" value="repair" />
              <el-option label="需更换" value="replace" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadMaintenanceData">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        empty-text="暂无数据"
      >
        <el-table-column prop="materialName" label="物资名称" width="180" show-overflow-tooltip />
        <el-table-column prop="warehouseName" label="库房" width="140" align="center" />
        <el-table-column prop="maintenanceType" label="保养类型" width="140" align="center">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.maintenanceType)" size="small">
              {{ getTypeText(row.maintenanceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="maintenanceDate" label="保养日期" width="140" align="center">
          <template #default="{ row }">
            {{ formatDate(row.maintenanceDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="maintenanceContent" label="保养内容" min-width="220" show-overflow-tooltip />
        <el-table-column prop="maintenanceResult" label="保养结果" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getResultTagType(row.maintenanceResult)" size="small">
              {{ getResultText(row.maintenanceResult) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operatorName" label="操作员" width="120" align="center" />
        <el-table-column prop="nextMaintenanceDate" label="下次保养" width="140" align="center">
          <template #default="{ row }">
            {{ formatDate(row.nextMaintenanceDate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" plain @click="showEditDialog(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="info" size="small" plain @click="viewDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button type="danger" size="small" plain @click="deleteMaintenance(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadMaintenanceData"
          @current-change="loadMaintenanceData"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="isEdit ? '编辑保养记录' : '新增保养记录'"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物资" prop="materialId">
              <el-select v-model="form.materialId" placeholder="请选择物资" style="width: 100%;" filterable>
                <el-option
                  v-for="material in materials"
                  :key="material.id"
                  :label="material.materialName"
                  :value="material.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库房" prop="warehouseId">
              <el-select v-model="form.warehouseId" placeholder="请选择库房" style="width: 100%;">
                <el-option label="主库房" :value="1" />
                <el-option label="备用库房" :value="2" />
                <el-option label="器材库" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="保养类型" prop="maintenanceType">
              <el-select v-model="form.maintenanceType" placeholder="请选择类型" style="width: 100%;">
                <el-option label="例行保养" value="routine" />
                <el-option label="专项检查" value="special" />
                <el-option label="紧急维修" value="emergency" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保养日期" prop="maintenanceDate">
              <el-date-picker
                v-model="form.maintenanceDate"
                type="date"
                placeholder="选择保养日期"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="保养内容" prop="maintenanceContent">
          <el-input
            v-model="form.maintenanceContent"
            type="textarea"
            :rows="3"
            placeholder="请输入保养内容"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="保养结果" prop="maintenanceResult">
              <el-select v-model="form.maintenanceResult" placeholder="请选择结果" style="width: 100%;">
                <el-option label="正常" value="normal" />
                <el-option label="需维修" value="repair" />
                <el-option label="需更换" value="replace" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作员" prop="operatorName">
              <el-input v-model="form.operatorName" placeholder="请输入操作员姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="下次保养日期">
          <el-date-picker
            v-model="form.nextMaintenanceDate"
            type="date"
            placeholder="选择下次保养日期"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="保养记录详情"
      v-model="detailDialogVisible"
      width="900px"
      :close-on-click-modal="false"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <View />
          </el-icon>
          <span>保养记录详情信息</span>
        </div>
      </template>

      <div v-loading="detailLoading" class="detail-content">
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">物资名称：</span>
                <span class="value">{{ detailData.materialName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">库房：</span>
                <span class="value">{{ detailData.warehouseName }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">保养类型：</span>
                <span class="value">{{ getMaintenanceTypeText(detailData.maintenanceType) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">保养日期：</span>
                <span class="value">{{ detailData.maintenanceDate }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item">
            <span class="label">操作员：</span>
            <span class="value">{{ detailData.operatorName }}</span>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">保养内容</div>
          <div class="detail-item">
            <span class="label">保养内容：</span>
            <span class="value">{{ detailData.maintenanceContent }}</span>
          </div>
          <div class="detail-item">
            <span class="label">保养结果：</span>
            <el-tag :type="getResultTagType(detailData.maintenanceResult)" size="small">
              {{ getMaintenanceResultText(detailData.maintenanceResult) }}
            </el-tag>
          </div>
          <div class="detail-item" v-if="detailData.nextMaintenanceDate">
            <span class="label">下次保养日期：</span>
            <span class="value">{{ detailData.nextMaintenanceDate }}</span>
          </div>
        </div>

        <div class="detail-section" v-if="detailData.remark">
          <div class="section-title">备注信息</div>
          <div class="detail-item">
            <span class="label">备注：</span>
            <span class="value">{{ detailData.remark }}</span>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">时间信息</div>
          <div class="detail-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ detailData.createTime }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { maintenanceApi } from '../../api/maintenance'
import { materialApi } from '../../api/material'
import { Plus, Search, Edit, Delete, View, Close } from '@element-plus/icons-vue'

const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const tableData = ref([])
const materials = ref([])
const formRef = ref()

const searchForm = reactive({
  keyword: '',
  maintenanceType: '',
  maintenanceResult: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const form = reactive({
  id: null,
  materialId: null,
  warehouseId: null,
  maintenanceType: '',
  maintenanceDate: new Date(),
  maintenanceContent: '',
  maintenanceResult: '',
  operatorName: '',
  nextMaintenanceDate: null,
  remark: ''
})

const rules = {
  materialId: [
    { required: true, message: '请选择物资', trigger: 'change' }
  ],
  warehouseId: [
    { required: true, message: '请选择库房', trigger: 'change' }
  ],
  maintenanceType: [
    { required: true, message: '请选择保养类型', trigger: 'change' }
  ],
  maintenanceDate: [
    { required: true, message: '请选择保养日期', trigger: 'change' }
  ],
  maintenanceContent: [
    { required: true, message: '请输入保养内容', trigger: 'blur' }
  ],
  maintenanceResult: [
    { required: true, message: '请选择保养结果', trigger: 'change' }
  ],
  operatorName: [
    { required: true, message: '请输入操作员姓名', trigger: 'blur' }
  ]
}

const loadMaintenanceData = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await maintenanceApi.getPage(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('加载保养记录失败')
  } finally {
    loading.value = false
  }
}

const loadMaterials = async () => {
  try {
    const response = await materialApi.getPage({ current: 1, size: 1000 })
    materials.value = response.data.records || []
  } catch (error) {
    ElMessage.error('加载物资数据失败')
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    maintenanceType: '',
    maintenanceResult: ''
  })
  pagination.current = 1
  loadMaintenanceData()
}

const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(form, {
    ...row,
    maintenanceDate: new Date(row.maintenanceDate),
    nextMaintenanceDate: row.nextMaintenanceDate ? new Date(row.nextMaintenanceDate) : null
  })
  dialogVisible.value = true
}

// 详情相关状态
const detailDialogVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

const viewDetail = async (row) => {
  try {
    detailLoading.value = true
    detailDialogVisible.value = true
    const response = await maintenanceApi.getDetail(row.id)
    detailData.value = response.data
  } catch (error) {
    ElMessage.error('获取详情失败')
    detailDialogVisible.value = false
  } finally {
    detailLoading.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    materialId: null,
    warehouseId: null,
    maintenanceType: '',
    maintenanceDate: new Date(),
    maintenanceContent: '',
    maintenanceResult: '',
    operatorName: '',
    nextMaintenanceDate: null,
    remark: ''
  })
  formRef.value?.resetFields()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await maintenanceApi.update(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await maintenanceApi.create(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadMaintenanceData()
  } catch (error) {
    if (error.errors) return
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

const deleteMaintenance = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条保养记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await maintenanceApi.delete(row.id)
    ElMessage.success('删除成功')
    loadMaintenanceData()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || '删除失败')
  }
}

const getTypeText = (type) => {
  const typeMap = {
    routine: '例行保养',
    special: '专项检查',
    emergency: '紧急维修'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    routine: 'primary',
    special: 'warning',
    emergency: 'danger'
  }
  return typeMap[type] || ''
}

const getResultText = (result) => {
  const resultMap = {
    normal: '正常',
    repair: '需维修',
    replace: '需更换'
  }
  return resultMap[result] || result
}

const getResultTagType = (result) => {
  const resultMap = {
    normal: 'success',
    repair: 'warning',
    replace: 'danger'
  }
  return resultMap[result] || ''
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString()
}

// 详情对话框使用的函数
const getMaintenanceTypeText = (type) => {
  return getTypeText(type)
}

const getMaintenanceResultText = (result) => {
  return getResultText(result)
}

onMounted(() => {
  loadMaintenanceData()
  loadMaterials()
})
</script>

<style scoped>
.maintenance-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 70px;
  margin: 0;
}

/* 详情对话框样式 */
.detail-content {
  min-height: 200px;
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  min-width: 100px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  word-break: break-all;
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
