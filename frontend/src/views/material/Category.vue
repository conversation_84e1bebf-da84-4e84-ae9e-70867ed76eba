<template>
  <div class="category-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>物资分类管理</span>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            新增分类
          </el-button>
        </div>
      </template>

      <!-- 分类树 -->
      <el-tree
        :data="categoryTree"
        :props="treeProps"
        node-key="id"
        :expand-on-click-node="false"
        class="category-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <div class="node-content">
              <span class="node-label">{{ data.categoryName }}</span>
              <span class="node-code">{{ data.categoryCode }}</span>
              <span class="node-level" :class="'level-' + data.level">
                {{ getLevelText(data.level) }}
              </span>
            </div>
            <div class="node-actions">
              <el-button size="small" type="text" @click="showEditDialog(data)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" type="text" @click="showAddChildDialog(data)" v-if="data.level < 3">
                <el-icon><Plus /></el-icon>
              </el-button>
              <el-button size="small" type="text" @click="deleteCategory(data)" style="color: #f56c6c;">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="resetForm"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <Plus v-if="!isEdit" />
            <Edit v-else />
          </el-icon>
          <span>{{ dialogTitle }}</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="90px"
        label-position="right"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-form-item label="分类名称" prop="categoryName">
            <el-input
              v-model="form.categoryName"
              placeholder="请输入分类名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="父级分类" v-if="!isAddChild">
            <el-select
              v-model="form.parentId"
              placeholder="请选择父级分类"
              style="width: 100%;"
              clearable
            >
              <el-option label="顶级分类" :value="0">
                <el-icon style="margin-right: 8px;"><House /></el-icon>
                顶级分类
              </el-option>
              <el-option
                v-for="category in parentCategories"
                :key="category.id"
                :label="category.categoryName"
                :value="category.id"
              >
                <el-icon style="margin-right: 8px;"><Folder /></el-icon>
                {{ category.categoryName }}
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-title">其他信息</div>
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息（可选）"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="loading" size="default">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { categoryApi } from '../../api/material'
import { Plus, Edit, Delete, Check, Close, House, Folder } from '@element-plus/icons-vue'

const categoryTree = ref([])
const dialogVisible = ref(false)
const loading = ref(false)
const isEdit = ref(false)
const isAddChild = ref(false)
const parentCategory = ref(null)
const formRef = ref()

const treeProps = {
  children: 'children',
  label: 'categoryName'
}

const form = reactive({
  id: null,
  categoryCode: '',
  categoryName: '',
  parentId: 0,
  level: 1,
  remark: ''
})

const rules = {
  categoryCode: [
    { min: 3, max: 20, message: '编码长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

const dialogTitle = computed(() => {
  if (isEdit.value) return '编辑分类'
  if (isAddChild.value) return '新增子分类'
  return '新增分类'
})

const parentCategories = computed(() => {
  // 获取所有可以作为父级的分类（一级和二级）
  const flattenCategories = (categories) => {
    let result = []
    categories.forEach(category => {
      if (category.level < 3) {  // 只有一级和二级可以作为父级
        result.push(category)
      }
      if (category.children && category.children.length > 0) {
        result = result.concat(flattenCategories(category.children))
      }
    })
    return result
  }
  return flattenCategories(categoryTree.value)
})

// 获取层级文本
const getLevelText = (level) => {
  const levelMap = {
    1: '一级',
    2: '二级',
    3: '三级'
  }
  return levelMap[level] || '未知'
}

const loadCategoryTree = async () => {
  try {
    const response = await categoryApi.getTree()
    categoryTree.value = response.data || []
  } catch (error) {
    ElMessage.error('加载分类数据失败')
  }
}

const showAddDialog = () => {
  isEdit.value = false
  isAddChild.value = false
  dialogVisible.value = true
}

const showEditDialog = (data) => {
  isEdit.value = true
  isAddChild.value = false
  Object.assign(form, data)
  dialogVisible.value = true
}

const showAddChildDialog = (data) => {
  isEdit.value = false
  isAddChild.value = true
  parentCategory.value = data
  form.parentId = data.id
  form.level = data.level + 1  // 动态计算子级层级
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    categoryCode: '',
    categoryName: '',
    parentId: 0,
    level: 1,
    remark: ''
  })
  isEdit.value = false
  isAddChild.value = false
  parentCategory.value = null
  formRef.value?.resetFields()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value) {
      await categoryApi.update(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await categoryApi.create(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadCategoryTree()
  } catch (error) {
    if (error.errors) return // 表单验证错误
    ElMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

const deleteCategory = async (data) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${data.categoryName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await categoryApi.delete(data.id)
    ElMessage.success('删除成功')
    loadCategoryTree()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || '删除失败')
  }
}

onMounted(() => {
  loadCategoryTree()
})
</script>

<style scoped>
.category-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-tree {
  margin-top: 20px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-label {
  font-weight: bold;
  margin-right: 12px;
}

.node-code {
  color: #909399;
  font-size: 12px;
  margin-right: 12px;
}

.node-level {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.level-1 {
  background-color: #409EFF;
}

.level-2 {
  background-color: #67C23A;
}

.level-3 {
  background-color: #E6A23C;
}

.node-actions {
  display: flex;
  gap: 8px;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}
</style>
