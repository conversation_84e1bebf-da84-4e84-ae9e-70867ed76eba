<template>
  <div class="material-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>物资台账管理</span>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            新增物资
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="物资名称/编码"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.categoryId" placeholder="请选择分类" style="width: 150px;" clearable>
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.categoryName"
                :value="category.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px;" clearable>
              <el-option label="正常" :value="1" />
              <el-option label="停用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadMaterials">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" v-loading="loading" stripe>
        <el-table-column prop="materialCode" label="物资编码" width="120" />
        <el-table-column prop="materialName" label="物资名称" min-width="150" />
        <el-table-column prop="categoryName" label="分类" width="120" />
        <el-table-column prop="specification" label="规格型号" width="150" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="brand" label="品牌" width="100" />
        <el-table-column prop="minStock" label="最低库存" width="100" />
        <el-table-column prop="maxStock" label="最高库存" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="text" @click="showEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="text" @click="viewDetail(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button size="small" type="text" @click="deleteMaterial(row)" style="color: #f56c6c;">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadMaterials"
          @current-change="loadMaterials"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="isEdit ? '编辑物资信息' : '新增物资信息'"
      v-model="dialogVisible"
      width="700px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="resetForm"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <Plus v-if="!isEdit" />
            <Edit v-else />
          </el-icon>
          <span>{{ isEdit ? '编辑物资信息' : '新增物资信息' }}</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="90px"
        label-position="right"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物资编码" prop="materialCode">
                <el-input
                  v-model="form.materialCode"
                  placeholder="请输入物资编码，如：MAT001"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物资名称" prop="materialName">
                <el-input
                  v-model="form.materialName"
                  placeholder="请输入物资名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分类" prop="categoryId">
                <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%;">
                  <el-option
                    v-for="category in categories"
                    :key="category.id"
                    :label="category.categoryName"
                    :value="category.id"
                  >
                    <el-icon style="margin-right: 8px;"><Folder /></el-icon>
                    {{ category.categoryName }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="计量单位" prop="unit">
                <el-input
                  v-model="form.unit"
                  placeholder="如：个、台、套"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 详细信息 -->
        <div class="form-section">
          <div class="section-title">详细信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="品牌">
                <el-input
                  v-model="form.brand"
                  placeholder="请输入品牌"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="型号">
                <el-input
                  v-model="form.model"
                  placeholder="请输入型号"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="规格型号">
            <el-input
              v-model="form.specification"
              placeholder="请输入详细规格型号"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="存储条件">
            <el-input
              v-model="form.storageCondition"
              placeholder="请输入存储条件要求"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>



        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-title">其他信息</div>
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息（可选）"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading" size="default">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="物资详情"
      v-model="detailDialogVisible"
      width="900px"
      :close-on-click-modal="false"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <View />
          </el-icon>
          <span>物资详情信息</span>
        </div>
      </template>

      <div v-loading="detailLoading" class="detail-content">
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">物资编码：</span>
                <span class="value">{{ detailData.materialCode }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">物资名称：</span>
                <span class="value">{{ detailData.materialName }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">规格型号：</span>
                <span class="value">{{ detailData.specification || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">计量单位：</span>
                <span class="value">{{ detailData.unit }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">品牌：</span>
                <span class="value">{{ detailData.brand || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">型号：</span>
                <span class="value">{{ detailData.model || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="detail-section">
          <div class="section-title">库存信息</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="label">最小库存：</span>
                <span class="value">{{ detailData.minStock }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="label">最大库存：</span>
                <span class="value">{{ detailData.maxStock }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="label">当前库存：</span>
                <span class="value">{{ detailData.currentStock || 0 }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item">
            <span class="label">存储条件：</span>
            <span class="value">{{ detailData.storageCondition || '-' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">状态信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">状态：</span>
                <el-tag :type="detailData.status === 1 ? 'success' : 'danger'" size="small">
                  <el-icon style="margin-right: 4px;">
                    <Check v-if="detailData.status === 1" />
                    <Close v-else />
                  </el-icon>
                  {{ detailData.status === 1 ? '正常' : '停用' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ detailData.createTime }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item" v-if="detailData.remark">
            <span class="label">备注：</span>
            <span class="value">{{ detailData.remark }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { materialApi, categoryApi } from '../../api/material'
import { Plus, Search, Edit, View, Delete, Check, Close, Folder } from '@element-plus/icons-vue'

const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const tableData = ref([])
const categories = ref([])
const formRef = ref()

const searchForm = reactive({
  keyword: '',
  categoryId: null,
  status: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const form = reactive({
  id: null,
  materialCode: '',
  materialName: '',
  categoryId: null,
  specification: '',
  unit: '',
  brand: '',
  model: '',
  status: 1,
  remark: ''
})

const rules = {
  materialCode: [
    { required: true, message: '请输入物资编码', trigger: 'blur' }
  ],
  materialName: [
    { required: true, message: '请输入物资名称', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  unit: [
    { required: true, message: '请输入计量单位', trigger: 'blur' }
  ]
}

const loadMaterials = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await materialApi.getPage(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await categoryApi.getList()
    categories.value = response.data || []
  } catch (error) {
    ElMessage.error('加载分类数据失败')
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    categoryId: null,
    status: null
  })
  pagination.current = 1
  loadMaterials()
}

const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(form, row)
  dialogVisible.value = true
}

// 详情相关状态
const detailDialogVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

const viewDetail = async (row) => {
  try {
    detailLoading.value = true
    detailDialogVisible.value = true
    const response = await materialApi.getDetail(row.id)
    detailData.value = response.data
  } catch (error) {
    ElMessage.error('获取详情失败')
    detailDialogVisible.value = false
  } finally {
    detailLoading.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    materialCode: '',
    materialName: '',
    categoryId: null,
    specification: '',
    unit: '',
    brand: '',
    model: '',
    minStock: 0,
    maxStock: 0,
    storageCondition: '',
    status: 1,
    remark: ''
  })
  formRef.value?.resetFields()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await materialApi.update(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await materialApi.create(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadMaterials()
  } catch (error) {
    if (error.errors) return
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

const deleteMaterial = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除物资"${row.materialName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await materialApi.delete(row.id)
    ElMessage.success('删除成功')
    loadMaterials()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || '删除失败')
  }
}

onMounted(() => {
  loadMaterials()
  loadCategories()
})
</script>

<style scoped>
.material-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.dialog-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 详情对话框样式 */
.detail-content {
  min-height: 200px;
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  min-width: 100px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  word-break: break-all;
}
</style>
