<template>
  <div class="warehouse-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>库室管理</span>
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            新增库室
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline label-width="60px">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="库室名称/编码/位置"
              style="width: 240px;"
              clearable
              prefix-icon="Search"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="全部状态"
              style="width: 120px;"
              clearable
            >
              <el-option label="正常" :value="1">
                <el-icon><Check /></el-icon>
                正常
              </el-option>
              <el-option label="停用" :value="0">
                <el-icon><Close /></el-icon>
                停用
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadWarehouses" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        empty-text="暂无数据"
      >
        <el-table-column prop="warehouseCode" label="库室编码" width="160" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.warehouseCode }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="warehouseName" label="库室名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="location" label="位置" width="220" show-overflow-tooltip />
        <el-table-column prop="managerName" label="管理员" width="120" align="center" />
        <el-table-column prop="managerPhone" label="联系电话" width="140" align="center">
          <template #default="{ row }">
            <span v-if="row.managerPhone">{{ row.managerPhone }}</span>
            <span v-else style="color: #c0c4cc;">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              <el-icon style="margin-right: 4px;">
                <Check v-if="row.status === 1" />
                <Close v-else />
              </el-icon>
              {{ row.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" plain @click="showEditDialog(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="info" size="small" plain @click="viewDetail(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button type="danger" size="small" plain @click="deleteWarehouse(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadWarehouses"
          @current-change="loadWarehouses"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="isEdit ? '编辑库室信息' : '新增库室信息'"
      v-model="dialogVisible"
      width="700px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="resetForm"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <Plus v-if="!isEdit" />
            <Edit v-else />
          </el-icon>
          <span>{{ isEdit ? '编辑库室信息' : '新增库室信息' }}</span>
        </div>
      </template>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="90px"
        label-position="right"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="库室编码" prop="warehouseCode">
                <el-input
                  v-model="form.warehouseCode"
                  placeholder="请输入库室编码"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="库室名称" prop="warehouseName">
                <el-input
                  v-model="form.warehouseName"
                  placeholder="请输入库室名称"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="位置" prop="location">
            <el-input
              v-model="form.location"
              placeholder="请输入详细位置，如：1号楼2层东侧"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 管理信息 -->
        <div class="form-section">
          <div class="section-title">管理信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管理员" prop="managerName">
                <el-input
                  v-model="form.managerName"
                  placeholder="请输入管理员姓名"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话">
                <el-input
                  v-model="form.managerPhone"
                  placeholder="请输入联系电话"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-title">其他信息</div>
          <el-form-item label="状态">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">
                <el-icon><Check /></el-icon>
                正常
              </el-radio>
              <el-radio :label="0">
                <el-icon><Close /></el-icon>
                停用
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息（可选）"
              maxlength="500"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading" size="default">
            <el-icon><Check /></el-icon>
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="库室详情"
      v-model="detailDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
    >
      <template #header>
        <div class="dialog-header">
          <el-icon style="margin-right: 8px; color: #409eff;">
            <View />
          </el-icon>
          <span>库室详情信息</span>
        </div>
      </template>

      <div v-loading="detailLoading" class="detail-content">
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">库室编码：</span>
                <span class="value">{{ detailData.warehouseCode }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">库室名称：</span>
                <span class="value">{{ detailData.warehouseName }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item">
            <span class="label">位置：</span>
            <span class="value">{{ detailData.location }}</span>
          </div>
        </div>

        <div class="detail-section">
          <div class="section-title">管理信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">管理员：</span>
                <span class="value">{{ detailData.managerName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ detailData.managerPhone || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="detail-section">
          <div class="section-title">状态信息</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">状态：</span>
                <el-tag :type="detailData.status === 1 ? 'success' : 'danger'" size="small">
                  <el-icon style="margin-right: 4px;">
                    <Check v-if="detailData.status === 1" />
                    <Close v-else />
                  </el-icon>
                  {{ detailData.status === 1 ? '正常' : '停用' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ detailData.createTime }}</span>
              </div>
            </el-col>
          </el-row>
          <div class="detail-item" v-if="detailData.remark">
            <span class="label">备注：</span>
            <span class="value">{{ detailData.remark }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false" size="default">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { warehouseApi } from '../../api/warehouse'
import { Plus, Search, Edit, View, Delete, Check, Close, Refresh } from '@element-plus/icons-vue'

const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const tableData = ref([])
const formRef = ref()

const searchForm = reactive({
  keyword: '',
  status: null
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const form = reactive({
  id: null,
  warehouseCode: '',
  warehouseName: '',
  location: '',
  managerName: '',
  managerPhone: '',
  status: 1,
  remark: ''
})

const rules = {
  warehouseCode: [
    { required: true, message: '请输入库室编码', trigger: 'blur' },
    { min: 2, max: 50, message: '编码长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9]+$/, message: '编码只能包含大写字母和数字', trigger: 'blur' }
  ],
  warehouseName: [
    { required: true, message: '请输入库室名称', trigger: 'blur' },
    { min: 2, max: 100, message: '名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入位置', trigger: 'blur' },
    { min: 2, max: 200, message: '位置长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  managerName: [
    { required: true, message: '请输入管理员姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  managerPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const loadWarehouses = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await warehouseApi.getPage(params)
    tableData.value = response.data.records || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: null
  })
  pagination.current = 1
  loadWarehouses()
}

const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(form, row)
  dialogVisible.value = true
}

// 详情相关状态
const detailDialogVisible = ref(false)
const detailData = ref({})
const detailLoading = ref(false)

const viewDetail = async (row) => {
  try {
    detailLoading.value = true
    detailDialogVisible.value = true
    const response = await warehouseApi.getDetail(row.id)
    detailData.value = response.data
  } catch (error) {
    ElMessage.error('获取详情失败')
    detailDialogVisible.value = false
  } finally {
    detailLoading.value = false
  }
}


const resetForm = () => {
  Object.assign(form, {
    id: null,
    warehouseCode: '',
    warehouseName: '',
    location: '',
    managerName: '',
    managerPhone: '',
    status: 1,
    remark: ''
  })
  formRef.value?.resetFields()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await warehouseApi.update(form.id, form)
      ElMessage.success('更新成功')
    } else {
      await warehouseApi.create(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadWarehouses()
  } catch (error) {
    if (error.errors) return
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

const deleteWarehouse = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除库室"${row.warehouseName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await warehouseApi.delete(row.id)
    ElMessage.success('删除成功')
    loadWarehouses()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error(error.message || '删除失败')
  }
}

onMounted(() => {
  loadWarehouses()
})
</script>

<style scoped>
.warehouse-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

/* 表单项样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-radio) {
  margin-right: 20px;
}

:deep(.el-radio__label) {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 对话框样式 */
.dialog-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  min-width: 70px;
  margin: 0;
}

/* 搜索区域样式优化 */
.search-area :deep(.el-form-item) {
  margin-bottom: 0;
}

.search-area :deep(.el-form-item__label) {
  font-weight: 500;
}

/* 卡片头部样式 */
.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header .el-button {
  font-weight: 500;
}

/* 详情对话框样式 */
.detail-content {
  min-height: 200px;
}

.detail-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  min-width: 100px;
  font-weight: 500;
  color: #606266;
  flex-shrink: 0;
}

.detail-item .value {
  color: #303133;
  word-break: break-all;
}
</style>
